{"name": "rendyr-broker-backend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "bun --hot src/index.ts", "dev:watch": "bun --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun --sourcemap", "start": "bun dist/index.js", "test": "bun test", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "db:studio": "drizzle-kit studio", "studio": "drizzle-kit studio", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "docker:clean": "docker compose down -v --remove-orphans"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "drizzle-orm": "^0.44.3", "hono": "^3.12.0", "ioredis": "^5.3.0", "postgres": "^3.4.0"}, "devDependencies": {"pg": "^8.16.3", "@types/bun": "^1.0.0", "@types/node": "^20.10.0", "@types/pg": "^8.15.4", "concurrently": "^8.2.2", "drizzle-kit": "^0.31.4", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}