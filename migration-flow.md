# LLM Migration Workflow - Quick Reference

## Core Commands
```bash
bun run db:generate    # Generate migration after schema changes
bun run db:migrate     # Apply migrations to database
bun run db:push        # Push schema changes directly (bypass migrations)
bun run db:pull        # Pull schema from database to local files
bun run db:studio      # Visual database inspection
bun run db:check       # Check for schema drift
bun run db:up          # Apply specific migration
```

## Standard Workflow
1. **Modify** schema in `src/schema.ts`
2. **Generate** migration: `bun run db:generate`
3. **Review** SQL in `src/migrations/` folder (numbered migration files)
4. **Apply** migration: `bun run db:migrate`
5. **Verify** with `bun run db:studio` or `bun run studio`

## Schema File Location
```typescript
// src/migrations/schema.ts (NOT src/schema/index.ts)
export const posts = pgTable('posts', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  title: text('title').notNull(),
  content: text('content'),
  authorId: uuid('author_id').references(() => usersInAuth.id),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
})
```

## Common Issues & Fixes
- **Migration conflicts**: Delete conflicting file, pull latest, regenerate
- **Schema drift**: Run `bun run db:check` then `bun run db:pull`
- **Failed migration**: Check with `bun run db:studio`, manually fix if needed
- **Auth schema conflicts**: Ensure you're only modifying `public` schema tables

## Production Deploy
1. Test migrations in development
2. Backup database
3. Deploy code
4. Run `bun run db:migrate` in production
5. Verify with health checks

## Multi-tenant Setup
This project uses Supabase Auth (`auth` schema) alongside custom tables in `public` schema:
- **Auth tables**: Managed by Supabase (users, sessions, etc.)
- **App tables**: Custom business logic (workspaces, listings, user_profiles)
- **Row Level Security**: Enabled for multi-tenant data isolation

## Key Rules
- Always review generated SQL before applying
- Never edit applied migration files
- Test migrations in development first
- Backup before production migrations
- Only modify tables in `public` schema (auth tables are managed by Supabase) 