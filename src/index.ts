import { <PERSON>o } from 'hono'
import routes from './routes'
import config, { validateConfig } from './config'
import { 
  corsMiddleware, 
  loggerMiddleware, 
  prettyJSONMiddleware, 
  errorHandler,
  validateJ<PERSON><PERSON> 
} from './middleware'

// Validate configuration on startup
try {
  validateConfig()
} catch (error) {
  console.error('❌ Configuration error:', error)
  process.exit(1)
}

const app = new Hono()

// Apply middleware
app.use('*', errorHandler)
app.use('*', loggerMiddleware)
app.use('*', prettyJSONMiddleware)
app.use('*', corsMiddleware)
app.use('*', validateJSON)

// Root endpoint
app.get('/', (c) => {
  const endpoints: Record<string, string> = {
    health: '/health',
    api: '/api',
    users: '/api/users',
    healthChecks: '/api/health-checks'
  }
  
  // Add dev endpoints only in development
  if (config.isDevelopment) {
    endpoints.dev = '/dev'
  }
  
  return c.json({
    message: 'Backend API Server',
    version: '1.0.0',
    environment: config.isDevelopment ? 'development' : 'production',
    timestamp: new Date().toISOString(),
    endpoints
  })
})

// Mount all routes
app.route('/', routes)

// Start the server
Bun.serve({
  fetch: app.fetch,
  port: config.port,
})

console.log(`🚀 Backend server is running on port ${config.port}`)
console.log(`📍 Server URL: http://localhost:${config.port}`)
console.log(`🔍 Health check: http://localhost:${config.port}/health`)
console.log(`🧪 API test: http://localhost:${config.port}/api/test`)
console.log(`🌍 Environment: ${config.isDevelopment ? 'development' : 'production'}`)
console.log(`⚡ Hot reload enabled`)

export default app
export { config }