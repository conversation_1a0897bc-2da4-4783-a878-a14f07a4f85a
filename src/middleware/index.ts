import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { HTTPException } from 'hono/http-exception'
import type { Context, Next } from 'hono'
import config from '../config'

// CORS middleware configuration
export const corsMiddleware = cors({
  origin: config.cors.origins,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
})

// Logger middleware
export const loggerMiddleware = logger()

// Pretty JSON middleware
export const prettyJSONMiddleware = prettyJSON()

// Error handling middleware
export const errorHandler = async (c: Context, next: Next) => {
  try {
    await next()
  } catch (err) {
    if (err instanceof HTTPException) {
      return err.getResponse()
    }
    
    console.error('Unhandled error:', err)
    return c.json({
      success: false,
      error: 'Internal server error',
      message: err instanceof Error ? err.message : 'Unknown error'
    }, 500)
  }
}

// Request validation middleware
export const validateJSON = async (c: Context, next: Next) => {
  if (c.req.method === 'POST' || c.req.method === 'PUT') {
    try {
      const contentType = c.req.header('content-type')
      if (contentType && contentType.includes('application/json')) {
        await c.req.json() // This will throw if invalid JSON
        // Reset the request body for the next handler
        c.req = c.req.clone()
      }
    } catch (error) {
      return c.json({
        success: false,
        error: 'Invalid JSON in request body'
      }, 400)
    }
  }
  await next()
}