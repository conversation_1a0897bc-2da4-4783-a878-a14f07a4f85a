CREATE TABLE "auth"."users" (
	"id" uuid PRIMARY KEY NOT NULL
);
--> statement-breakpoint
ALTER TABLE "listings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_profiles" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "workspace_invitations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "workspaces" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "listings" DROP CONSTRAINT "listings_assigned_to_fkey";
--> statement-breakpoint
ALTER TABLE "listings" DROP CONSTRAINT "listings_created_by_fkey";
--> statement-breakpoint
ALTER TABLE "user_profiles" DROP CONSTRAINT "user_profiles_invited_by_fkey";
--> statement-breakpoint
ALTER TABLE "user_profiles" DROP CONSTRAINT "user_profiles_user_id_fkey1";
--> statement-breakpoint
ALTER TABLE "user_profiles" DROP CONSTRAINT "user_profiles_user_id_fkey";
--> statement-breakpoint
DROP INDEX "idx_workspaces_status";--> statement-breakpoint
DROP INDEX "idx_workspaces_subscription_plan";--> statement-breakpoint
DROP INDEX "idx_workspaces_trial_ends_at";--> statement-breakpoint
DROP INDEX "idx_workspace_invitations_workspace_id";--> statement-breakpoint
DROP INDEX "idx_workspace_invitations_email";--> statement-breakpoint
DROP INDEX "idx_workspace_invitations_token";--> statement-breakpoint
DROP INDEX "idx_workspace_invitations_expires_at";--> statement-breakpoint
DROP INDEX "idx_listings_workspace_id";--> statement-breakpoint
DROP INDEX "idx_listings_created_by";--> statement-breakpoint
DROP INDEX "idx_listings_assigned_to";--> statement-breakpoint
DROP INDEX "idx_listings_status";--> statement-breakpoint
DROP INDEX "idx_listings_listing_type";--> statement-breakpoint
DROP INDEX "idx_listings_city_state";--> statement-breakpoint
DROP INDEX "idx_listings_price";--> statement-breakpoint
DROP INDEX "idx_listings_created_at";--> statement-breakpoint
DROP INDEX "idx_user_profiles_user_id";--> statement-breakpoint
DROP INDEX "idx_user_profiles_workspace_id";--> statement-breakpoint
DROP INDEX "idx_user_profiles_role";--> statement-breakpoint
DROP INDEX "idx_user_profiles_email";--> statement-breakpoint
DROP INDEX "idx_user_profiles_active";--> statement-breakpoint
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_workspaces_status" ON "workspaces" USING btree ("status" text_ops);--> statement-breakpoint
CREATE INDEX "idx_workspaces_subscription_plan" ON "workspaces" USING btree ("subscription_plan" text_ops);--> statement-breakpoint
CREATE INDEX "idx_workspaces_trial_ends_at" ON "workspaces" USING btree ("trial_ends_at" timestamptz_ops) WHERE (status = 'trial'::text);--> statement-breakpoint
CREATE INDEX "idx_workspace_invitations_workspace_id" ON "workspace_invitations" USING btree ("workspace_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_workspace_invitations_email" ON "workspace_invitations" USING btree ("email" text_ops);--> statement-breakpoint
CREATE INDEX "idx_workspace_invitations_token" ON "workspace_invitations" USING btree ("token" text_ops);--> statement-breakpoint
CREATE INDEX "idx_workspace_invitations_expires_at" ON "workspace_invitations" USING btree ("expires_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_workspace_id" ON "listings" USING btree ("workspace_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_created_by" ON "listings" USING btree ("created_by" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_assigned_to" ON "listings" USING btree ("assigned_to" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_status" ON "listings" USING btree ("status" text_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_listing_type" ON "listings" USING btree ("listing_type" text_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_city_state" ON "listings" USING btree ("city" text_ops,"state" text_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_price" ON "listings" USING btree ("price" numeric_ops);--> statement-breakpoint
CREATE INDEX "idx_listings_created_at" ON "listings" USING btree ("created_at" timestamptz_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_user_id" ON "user_profiles" USING btree ("id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_workspace_id" ON "user_profiles" USING btree ("workspace_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_role" ON "user_profiles" USING btree ("role" text_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_email" ON "user_profiles" USING btree ("email" text_ops);--> statement-breakpoint
CREATE INDEX "idx_user_profiles_active" ON "user_profiles" USING btree ("is_active" bool_ops) WHERE (is_active = true);--> statement-breakpoint
ALTER TABLE "workspaces" ADD CONSTRAINT "workspaces_company_type_check" CHECK (company_type = ANY (ARRAY['individual'::text, 'team'::text, 'firm'::text]));--> statement-breakpoint
ALTER TABLE "workspaces" ADD CONSTRAINT "workspaces_subscription_plan_check" CHECK (subscription_plan = ANY (ARRAY['trial'::text, 'basic'::text, 'pro'::text, 'enterprise'::text]));--> statement-breakpoint
ALTER TABLE "workspaces" ADD CONSTRAINT "workspaces_status_check" CHECK (status = ANY (ARRAY['active'::text, 'suspended'::text, 'trial'::text, 'cancelled'::text]));--> statement-breakpoint
ALTER TABLE "workspace_invitations" ADD CONSTRAINT "workspace_invitations_role_check" CHECK (role = ANY (ARRAY['admin'::text, 'broker'::text, 'viewer'::text]));--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_status_check" CHECK (status = ANY (ARRAY['draft'::text, 'active'::text, 'pending'::text, 'sold'::text, 'withdrawn'::text]));--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_listing_type_check" CHECK (listing_type = ANY (ARRAY['sale'::text, 'lease'::text, 'business_sale'::text]));--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_team_visibility_check" CHECK (team_visibility = ANY (ARRAY['all'::text, 'assigned'::text, 'custom'::text]));--> statement-breakpoint
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_role_check" CHECK (role = ANY (ARRAY['owner'::text, 'admin'::text, 'broker'::text, 'viewer'::text]));--> statement-breakpoint
CREATE POLICY "workspace_access_policy" ON "workspaces" AS PERMISSIVE FOR SELECT TO "authenticated" USING (id = get_user_workspace_id());--> statement-breakpoint
CREATE POLICY "workspace_update_policy" ON "workspaces" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (id = get_user_workspace_id() AND user_has_role('owner'));--> statement-breakpoint
CREATE POLICY "workspace_create_policy" ON "workspaces" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (auth.uid() IS NOT NULL);--> statement-breakpoint
CREATE POLICY "workspace_delete_policy" ON "workspaces" AS PERMISSIVE FOR DELETE TO "authenticated" USING (id = get_user_workspace_id() AND user_has_role('owner'));--> statement-breakpoint
CREATE POLICY "invitations_select_policy" ON "workspace_invitations" AS PERMISSIVE FOR SELECT TO "authenticated" USING (workspace_id = get_user_workspace_id() AND user_has_min_role('admin'));--> statement-breakpoint
CREATE POLICY "invitations_insert_policy" ON "workspace_invitations" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND invited_by = auth.uid());--> statement-breakpoint
CREATE POLICY "invitations_update_policy" ON "workspace_invitations" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (workspace_id = get_user_workspace_id() AND user_has_min_role('admin'));--> statement-breakpoint
CREATE POLICY "invitations_delete_policy" ON "workspace_invitations" AS PERMISSIVE FOR DELETE TO "authenticated" USING (workspace_id = get_user_workspace_id() AND user_has_min_role('admin'));--> statement-breakpoint
CREATE POLICY "invitations_public_token_access" ON "workspace_invitations" AS PERMISSIVE FOR SELECT TO "anon", "authenticated" USING (token IS NOT NULL AND expires_at > NOW() AND accepted_at IS NULL);--> statement-breakpoint
CREATE POLICY "listings_select_policy" ON "listings" AS PERMISSIVE FOR SELECT TO "authenticated" USING (workspace_id = get_user_workspace_id() AND (team_visibility = 'all' OR assigned_to = auth.uid() OR created_by = auth.uid() OR user_has_min_role('admin')));--> statement-breakpoint
CREATE POLICY "listings_insert_policy" ON "listings" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (workspace_id = get_user_workspace_id() AND user_has_min_role('broker') AND created_by = auth.uid());--> statement-breakpoint
CREATE POLICY "listings_update_policy" ON "listings" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR assigned_to = auth.uid() OR user_has_min_role('admin')));--> statement-breakpoint
CREATE POLICY "listings_delete_policy" ON "listings" AS PERMISSIVE FOR DELETE TO "authenticated" USING (workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR user_has_min_role('admin')));--> statement-breakpoint
CREATE POLICY "user_profiles_select_policy" ON "user_profiles" AS PERMISSIVE FOR SELECT TO "authenticated" USING (workspace_id = get_user_workspace_id() OR id = auth.uid());--> statement-breakpoint
CREATE POLICY "user_profiles_update_policy" ON "user_profiles" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (id = auth.uid() OR (workspace_id = get_user_workspace_id() AND user_has_min_role('admin')));--> statement-breakpoint
CREATE POLICY "user_profiles_insert_policy" ON "user_profiles" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (workspace_id = get_user_workspace_id() AND (user_has_min_role('admin') OR id = auth.uid()));--> statement-breakpoint
CREATE POLICY "user_profiles_delete_policy" ON "user_profiles" AS PERMISSIVE FOR DELETE TO "authenticated" USING (workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND id != auth.uid());--> statement-breakpoint
DROP TYPE "public"."request_status";--> statement-breakpoint
DROP TYPE "public"."key_status";--> statement-breakpoint
DROP TYPE "public"."key_type";--> statement-breakpoint
DROP TYPE "public"."factor_type";--> statement-breakpoint
DROP TYPE "public"."factor_status";--> statement-breakpoint
DROP TYPE "public"."aal_level";--> statement-breakpoint
DROP TYPE "public"."code_challenge_method";--> statement-breakpoint
DROP TYPE "public"."one_time_token_type";--> statement-breakpoint
DROP TYPE "public"."equality_op";--> statement-breakpoint
DROP TYPE "public"."action";