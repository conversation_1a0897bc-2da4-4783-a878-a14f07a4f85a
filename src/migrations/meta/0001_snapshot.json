{"id": "31455fe5-1f29-4fa3-9118-da64a7593d5d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"auth.users": {"name": "users", "schema": "auth", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "square_footage": {"name": "square_footage", "type": "integer", "primaryKey": false, "notNull": false}, "lot_size": {"name": "lot_size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "listing_type": {"name": "listing_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'sale'"}, "team_visibility": {"name": "team_visibility", "type": "text", "primaryKey": false, "notNull": false, "default": "'all'"}, "internal_notes": {"name": "internal_notes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "photos": {"name": "photos", "type": "text[]", "primaryKey": false, "notNull": false}, "documents": {"name": "documents", "type": "text[]", "primaryKey": false, "notNull": false}, "featured_photo": {"name": "featured_photo", "type": "text", "primaryKey": false, "notNull": false}, "virtual_tour_url": {"name": "virtual_tour_url", "type": "text", "primaryKey": false, "notNull": false}, "mls_number": {"name": "mls_number", "type": "text", "primaryKey": false, "notNull": false}, "listing_date": {"name": "listing_date", "type": "date", "primaryKey": false, "notNull": false}, "expiration_date": {"name": "expiration_date", "type": "date", "primaryKey": false, "notNull": false}, "days_on_market": {"name": "days_on_market", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listings_assigned_to": {"name": "idx_listings_assigned_to", "columns": [{"expression": "assigned_to", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_city_state": {"name": "idx_listings_city_state", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "state", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_at": {"name": "idx_listings_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last", "opclass": "timestamptz_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_by": {"name": "idx_listings_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_listing_type": {"name": "idx_listings_listing_type", "columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_price": {"name": "idx_listings_price", "columns": [{"expression": "price", "isExpression": false, "asc": true, "nulls": "last", "opclass": "numeric_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_status": {"name": "idx_listings_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_workspace_id": {"name": "idx_listings_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listings_workspace_id_fkey": {"name": "listings_workspace_id_fkey", "tableFrom": "listings", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"listings_select_policy": {"name": "listings_select_policy", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND (team_visibility = 'all' OR assigned_to = auth.uid() OR created_by = auth.uid() OR user_has_min_role('admin'))"}, "listings_insert_policy": {"name": "listings_insert_policy", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "workspace_id = get_user_workspace_id() AND user_has_min_role('broker') AND created_by = auth.uid()"}, "listings_update_policy": {"name": "listings_update_policy", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR assigned_to = auth.uid() OR user_has_min_role('admin'))"}, "listings_delete_policy": {"name": "listings_delete_policy", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR user_has_min_role('admin'))"}}, "checkConstraints": {"listings_status_check": {"name": "listings_status_check", "value": "status = ANY (ARRAY['draft'::text, 'active'::text, 'pending'::text, 'sold'::text, 'withdrawn'::text])"}, "listings_listing_type_check": {"name": "listings_listing_type_check", "value": "listing_type = ANY (ARRAY['sale'::text, 'lease'::text, 'business_sale'::text])"}, "listings_team_visibility_check": {"name": "listings_team_visibility_check", "value": "team_visibility = ANY (ARRAY['all'::text, 'assigned'::text, 'custom'::text])"}}, "isRLSEnabled": false}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_profiles_active": {"name": "idx_user_profiles_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last", "opclass": "bool_ops"}], "isUnique": false, "where": "(is_active = true)", "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_email": {"name": "idx_user_profiles_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_role": {"name": "idx_user_profiles_role", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_user_id": {"name": "idx_user_profiles_user_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_workspace_id": {"name": "idx_user_profiles_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_profiles_workspace_id_fkey": {"name": "user_profiles_workspace_id_fkey", "tableFrom": "user_profiles", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_profiles_user_id_fkey": {"name": "user_profiles_user_id_fkey", "tableFrom": "user_profiles", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_workspace_email": {"name": "unique_workspace_email", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}}, "policies": {"user_profiles_select_policy": {"name": "user_profiles_select_policy", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() OR id = auth.uid()"}, "user_profiles_update_policy": {"name": "user_profiles_update_policy", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "id = auth.uid() OR (workspace_id = get_user_workspace_id() AND user_has_min_role('admin'))"}, "user_profiles_insert_policy": {"name": "user_profiles_insert_policy", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "workspace_id = get_user_workspace_id() AND (user_has_min_role('admin') OR id = auth.uid())"}, "user_profiles_delete_policy": {"name": "user_profiles_delete_policy", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND id != auth.uid()"}}, "checkConstraints": {"user_profiles_role_check": {"name": "user_profiles_role_check", "value": "role = ANY (ARRAY['owner'::text, 'admin'::text, 'broker'::text, 'viewer'::text])"}}, "isRLSEnabled": false}, "public.workspace_invitations": {"name": "workspace_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspace_invitations_email": {"name": "idx_workspace_invitations_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_expires_at": {"name": "idx_workspace_invitations_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last", "opclass": "timestamptz_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_token": {"name": "idx_workspace_invitations_token", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_workspace_id": {"name": "idx_workspace_invitations_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last", "opclass": "uuid_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workspace_invitations_workspace_id_fkey": {"name": "workspace_invitations_workspace_id_fkey", "tableFrom": "workspace_invitations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workspace_invitations_invited_by_fkey": {"name": "workspace_invitations_invited_by_fkey", "tableFrom": "workspace_invitations", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspace_invitations_workspace_id_email_key": {"name": "workspace_invitations_workspace_id_email_key", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}, "workspace_invitations_token_key": {"name": "workspace_invitations_token_key", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {"invitations_select_policy": {"name": "invitations_select_policy", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND user_has_min_role('admin')"}, "invitations_insert_policy": {"name": "invitations_insert_policy", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND invited_by = auth.uid()"}, "invitations_update_policy": {"name": "invitations_update_policy", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND user_has_min_role('admin')"}, "invitations_delete_policy": {"name": "invitations_delete_policy", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "workspace_id = get_user_workspace_id() AND user_has_min_role('admin')"}, "invitations_public_token_access": {"name": "invitations_public_token_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["anon", "authenticated"], "using": "token IS NOT NULL AND expires_at > NOW() AND accepted_at IS NULL"}}, "checkConstraints": {"workspace_invitations_role_check": {"name": "workspace_invitations_role_check", "value": "role = ANY (ARRAY['admin'::text, 'broker'::text, 'viewer'::text])"}}, "isRLSEnabled": false}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "company_type": {"name": "company_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'team'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "target_markets": {"name": "target_markets", "type": "text[]", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "(now() + '14 days'::interval)"}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspaces_status": {"name": "idx_workspaces_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_subscription_plan": {"name": "idx_workspaces_subscription_plan", "columns": [{"expression": "subscription_plan", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_trial_ends_at": {"name": "idx_workspaces_trial_ends_at", "columns": [{"expression": "trial_ends_at", "isExpression": false, "asc": true, "nulls": "last", "opclass": "timestamptz_ops"}], "isUnique": false, "where": "(status = 'trial'::text)", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_domain_key": {"name": "workspaces_domain_key", "nullsNotDistinct": false, "columns": ["domain"]}}, "policies": {"workspace_access_policy": {"name": "workspace_access_policy", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "id = get_user_workspace_id()"}, "workspace_update_policy": {"name": "workspace_update_policy", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "id = get_user_workspace_id() AND user_has_role('owner')"}, "workspace_create_policy": {"name": "workspace_create_policy", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "auth.uid() IS NOT NULL"}, "workspace_delete_policy": {"name": "workspace_delete_policy", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "id = get_user_workspace_id() AND user_has_role('owner')"}}, "checkConstraints": {"workspaces_company_type_check": {"name": "workspaces_company_type_check", "value": "company_type = ANY (ARRAY['individual'::text, 'team'::text, 'firm'::text])"}, "workspaces_subscription_plan_check": {"name": "workspaces_subscription_plan_check", "value": "subscription_plan = ANY (ARRAY['trial'::text, 'basic'::text, 'pro'::text, 'enterprise'::text])"}, "workspaces_status_check": {"name": "workspaces_status_check", "value": "status = ANY (ARRAY['active'::text, 'suspended'::text, 'trial'::text, 'cancelled'::text])"}}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}