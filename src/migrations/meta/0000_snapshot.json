{"version": "7", "dialect": "postgresql", "tables": {"public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "company_type": {"name": "company_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'team'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "target_markets": {"name": "target_markets", "type": "text[]", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "(now() + '14 days'::interval)"}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspaces_status": {"columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspaces_status", "isUnique": false, "method": "btree", "concurrently": false}, "idx_workspaces_subscription_plan": {"columns": [{"expression": "subscription_plan", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspaces_subscription_plan", "isUnique": false, "method": "btree", "concurrently": false}, "idx_workspaces_trial_ends_at": {"columns": [{"expression": "trial_ends_at", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspaces_trial_ends_at", "isUnique": false, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_domain_key": {"name": "workspaces_domain_key", "columns": ["domain"], "nullsNotDistinct": false}}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.workspace_invitations": {"name": "workspace_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspace_invitations_workspace_id": {"columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspace_invitations_workspace_id", "isUnique": false, "method": "btree", "concurrently": false}, "idx_workspace_invitations_email": {"columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspace_invitations_email", "isUnique": false, "method": "btree", "concurrently": false}, "idx_workspace_invitations_token": {"columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspace_invitations_token", "isUnique": false, "method": "btree", "concurrently": false}, "idx_workspace_invitations_expires_at": {"columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_workspace_invitations_expires_at", "isUnique": false, "method": "btree", "concurrently": false}}, "foreignKeys": {"workspace_invitations_invited_by_fkey": {"name": "workspace_invitations_invited_by_fkey", "tableFrom": "workspace_invitations", "columnsFrom": ["invited_by"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "workspace_invitations_workspace_id_fkey": {"name": "workspace_invitations_workspace_id_fkey", "tableFrom": "workspace_invitations", "columnsFrom": ["workspace_id"], "tableTo": "workspaces", "schemaTo": "public", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspace_invitations_workspace_id_email_key": {"name": "workspace_invitations_workspace_id_email_key", "columns": ["workspace_id", "email"], "nullsNotDistinct": false}, "workspace_invitations_token_key": {"name": "workspace_invitations_token_key", "columns": ["token"], "nullsNotDistinct": false}}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "square_footage": {"name": "square_footage", "type": "integer", "primaryKey": false, "notNull": false}, "lot_size": {"name": "lot_size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "listing_type": {"name": "listing_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'sale'"}, "team_visibility": {"name": "team_visibility", "type": "text", "primaryKey": false, "notNull": false, "default": "'all'"}, "internal_notes": {"name": "internal_notes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "photos": {"name": "photos", "type": "text[]", "primaryKey": false, "notNull": false}, "documents": {"name": "documents", "type": "text[]", "primaryKey": false, "notNull": false}, "featured_photo": {"name": "featured_photo", "type": "text", "primaryKey": false, "notNull": false}, "virtual_tour_url": {"name": "virtual_tour_url", "type": "text", "primaryKey": false, "notNull": false}, "mls_number": {"name": "mls_number", "type": "text", "primaryKey": false, "notNull": false}, "listing_date": {"name": "listing_date", "type": "date", "primaryKey": false, "notNull": false}, "expiration_date": {"name": "expiration_date", "type": "date", "primaryKey": false, "notNull": false}, "days_on_market": {"name": "days_on_market", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listings_workspace_id": {"columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_workspace_id", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_created_by": {"columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_created_by", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_assigned_to": {"columns": [{"expression": "assigned_to", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_assigned_to", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_status": {"columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_status", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_listing_type": {"columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_listing_type", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_city_state": {"columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_city_state", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_price": {"columns": [{"expression": "price", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_price", "isUnique": false, "method": "btree", "concurrently": false}, "idx_listings_created_at": {"columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_listings_created_at", "isUnique": false, "method": "btree", "concurrently": false}}, "foreignKeys": {"listings_assigned_to_fkey": {"name": "listings_assigned_to_fkey", "tableFrom": "listings", "columnsFrom": ["assigned_to"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "listings_created_by_fkey": {"name": "listings_created_by_fkey", "tableFrom": "listings", "columnsFrom": ["created_by"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "listings_workspace_id_fkey": {"name": "listings_workspace_id_fkey", "tableFrom": "listings", "columnsFrom": ["workspace_id"], "tableTo": "workspaces", "schemaTo": "public", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_profiles_user_id": {"columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_user_profiles_user_id", "isUnique": false, "method": "btree", "concurrently": false}, "idx_user_profiles_workspace_id": {"columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_user_profiles_workspace_id", "isUnique": false, "method": "btree", "concurrently": false}, "idx_user_profiles_role": {"columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_user_profiles_role", "isUnique": false, "method": "btree", "concurrently": false}, "idx_user_profiles_email": {"columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_user_profiles_email", "isUnique": false, "method": "btree", "concurrently": false}, "idx_user_profiles_active": {"columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "idx_user_profiles_active", "isUnique": false, "method": "btree", "concurrently": false}}, "foreignKeys": {"user_profiles_invited_by_fkey": {"name": "user_profiles_invited_by_fkey", "tableFrom": "user_profiles", "columnsFrom": ["invited_by"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "user_profiles_user_id_fkey": {"name": "user_profiles_user_id_fkey", "tableFrom": "user_profiles", "columnsFrom": ["id"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "user_profiles_user_id_fkey1": {"name": "user_profiles_user_id_fkey1", "tableFrom": "user_profiles", "columnsFrom": ["user_id"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}, "user_profiles_workspace_id_fkey": {"name": "user_profiles_workspace_id_fkey", "tableFrom": "user_profiles", "columnsFrom": ["workspace_id"], "tableTo": "workspaces", "schemaTo": "public", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_workspace_email": {"name": "unique_workspace_email", "columns": ["workspace_id", "email"], "nullsNotDistinct": false}}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}}, "enums": {"public.request_status": {"name": "request_status", "schema": "public", "values": ["PENDING", "SUCCESS", "ERROR"]}, "public.key_status": {"name": "key_status", "schema": "public", "values": ["default", "valid", "invalid", "expired"]}, "public.key_type": {"name": "key_type", "schema": "public", "values": ["aead-ietf", "aead-det", "hmacsha512", "hmacsha256", "auth", "<PERSON><PERSON>h", "<PERSON><PERSON><PERSON>", "kdf", "secretbox", "secretstream", "stream_xchacha20"]}, "public.factor_type": {"name": "factor_type", "schema": "public", "values": ["totp", "webauthn", "phone"]}, "public.factor_status": {"name": "factor_status", "schema": "public", "values": ["unverified", "verified"]}, "public.aal_level": {"name": "aal_level", "schema": "public", "values": ["aal1", "aal2", "aal3"]}, "public.code_challenge_method": {"name": "code_challenge_method", "schema": "public", "values": ["s256", "plain"]}, "public.one_time_token_type": {"name": "one_time_token_type", "schema": "public", "values": ["confirmation_token", "reauthentication_token", "recovery_token", "email_change_token_new", "email_change_token_current", "phone_change_token"]}, "public.equality_op": {"name": "equality_op", "schema": "public", "values": ["eq", "neq", "lt", "lte", "gt", "gte", "in"]}, "public.action": {"name": "action", "schema": "public", "values": ["INSERT", "UPDATE", "DELETE", "TRUNCATE", "ERROR"]}}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "00000000-0000-0000-0000-000000000000", "prevId": "", "sequences": {}, "policies": {}, "views": {}, "roles": {}}