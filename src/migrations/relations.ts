import { relations } from "drizzle-orm/relations";
import { workspaces, workspaceInvitations, usersInAuth, listings, userProfiles } from "./schema";

export const workspaceInvitationsRelations = relations(workspaceInvitations, ({one}) => ({
	workspace: one(workspaces, {
		fields: [workspaceInvitations.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [workspaceInvitations.invitedBy],
		references: [usersInAuth.id]
	}),
}));

export const workspacesRelations = relations(workspaces, ({many}) => ({
	workspaceInvitations: many(workspaceInvitations),
	listings: many(listings),
	userProfiles: many(userProfiles),
}));

export const usersInAuthRelations = relations(usersInAuth, ({many}) => ({
	workspaceInvitations: many(workspaceInvitations),
	listings_createdBy: many(listings, {
		relationName: "listings_createdBy_usersInAuth_id"
	}),
	listings_assignedTo: many(listings, {
		relationName: "listings_assignedTo_usersInAuth_id"
	}),
	userProfiles_id: many(userProfiles, {
		relationName: "userProfiles_id_usersInAuth_id"
	}),
	userProfiles_invitedBy: many(userProfiles, {
		relationName: "userProfiles_invitedBy_usersInAuth_id"
	}),
	userProfiles_userId: many(userProfiles, {
		relationName: "userProfiles_userId_usersInAuth_id"
	}),
}));

export const listingsRelations = relations(listings, ({one}) => ({
	usersInAuth_createdBy: one(usersInAuth, {
		fields: [listings.createdBy],
		references: [usersInAuth.id],
		relationName: "listings_createdBy_usersInAuth_id"
	}),
	workspace: one(workspaces, {
		fields: [listings.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth_assignedTo: one(usersInAuth, {
		fields: [listings.assignedTo],
		references: [usersInAuth.id],
		relationName: "listings_assignedTo_usersInAuth_id"
	}),
}));

export const userProfilesRelations = relations(userProfiles, ({one}) => ({
	usersInAuth_id: one(usersInAuth, {
		fields: [userProfiles.id],
		references: [usersInAuth.id],
		relationName: "userProfiles_id_usersInAuth_id"
	}),
	workspace: one(workspaces, {
		fields: [userProfiles.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth_invitedBy: one(usersInAuth, {
		fields: [userProfiles.invitedBy],
		references: [usersInAuth.id],
		relationName: "userProfiles_invitedBy_usersInAuth_id"
	}),
	usersInAuth_userId: one(usersInAuth, {
		fields: [userProfiles.userId],
		references: [usersInAuth.id],
		relationName: "userProfiles_userId_usersInAuth_id"
	}),
}));