-- Multi-tenant workspace schema migration
-- This migration creates the workspace-centric multi-tenant architecture

-- Create workspaces table
CREATE TABLE IF NOT EXISTS workspaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name TEXT NOT NULL,
    company_type TEXT CHECK (company_type IN ('individual', 'team', 'firm')) DEFAULT 'team',
    subscription_plan TEXT CHECK (subscription_plan IN ('trial', 'basic', 'pro', 'enterprise')) DEFAULT 'trial',
    domain TEXT UNIQUE,
    logo_url TEXT,
    primary_color TEXT DEFAULT '#3B82F6',
    address TEXT,
    phone TEXT,
    website TEXT,
    license_number TEXT,
    specialties TEXT[],
    target_markets TEXT[],
    status TEXT CHECK (status IN ('active', 'suspended', 'trial', 'cancelled')) DEFAULT 'trial',
    trial_ends_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '14 days'),
    onboarding_completed BOOLEA<PERSON> DEFAULT FALSE,
    onboarding_step INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workspace_invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT CHECK (role IN ('admin', 'broker', 'viewer')) NOT NULL,
    invited_by UUID REFERENCES auth.users(id),
    token TEXT UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(workspace_id, email)
);

-- Update user_profiles table to include workspace context
-- First, add new columns to existing user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS first_name TEXT,
ADD COLUMN IF NOT EXISTS last_name TEXT,
ADD COLUMN IF NOT EXISTS role TEXT CHECK (role IN ('owner', 'admin', 'broker', 'viewer')),
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS license_number TEXT,
ADD COLUMN IF NOT EXISTS avatar_url TEXT,
ADD COLUMN IF NOT EXISTS specialties TEXT[],
ADD COLUMN IF NOT EXISTS invited_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS joined_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES auth.users(id);

-- Update user_profiles primary key to use UUID from auth.users
-- Note: This requires careful handling of existing data
DO $$
BEGIN
    -- Check if we need to update the primary key structure
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_profiles' 
               AND column_name = 'id' 
               AND data_type = 'integer') THEN
        
        -- Drop the old serial id column and make user_id the primary key
        ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_pkey;
        ALTER TABLE user_profiles DROP COLUMN IF EXISTS id;
        ALTER TABLE user_profiles ADD PRIMARY KEY (user_id);
        
        -- Rename user_id to id for consistency
        ALTER TABLE user_profiles RENAME COLUMN user_id TO id;
    END IF;
END $$;

-- Add unique constraint for workspace + email combination
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'unique_workspace_email') THEN
        ALTER TABLE user_profiles ADD CONSTRAINT unique_workspace_email UNIQUE(workspace_id, email);
    END IF;
END $$;

-- Create listings table if it doesn't exist
CREATE TABLE IF NOT EXISTS listings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) NOT NULL,
    assigned_to UUID REFERENCES auth.users(id),
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(12,2),
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    property_type TEXT,
    square_footage INTEGER,
    lot_size DECIMAL(10,2),
    year_built INTEGER,
    bedrooms INTEGER,
    bathrooms DECIMAL(3,1),
    status TEXT CHECK (status IN ('draft', 'active', 'pending', 'sold', 'withdrawn')) DEFAULT 'draft',
    listing_type TEXT CHECK (listing_type IN ('sale', 'lease', 'business_sale')) DEFAULT 'sale',
    team_visibility TEXT CHECK (team_visibility IN ('all', 'assigned', 'custom')) DEFAULT 'all',
    internal_notes JSONB DEFAULT '[]'::jsonb,
    photos TEXT[],
    documents TEXT[],
    featured_photo TEXT,
    virtual_tour_url TEXT,
    mls_number TEXT,
    listing_date DATE,
    expiration_date DATE,
    days_on_market INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- If listings table already exists, add workspace_id column
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'listings') THEN
        -- Add workspace_id if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'listings' AND column_name = 'workspace_id') THEN
            ALTER TABLE listings ADD COLUMN workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE;
        END IF;
        
        -- Add other workspace-related columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'listings' AND column_name = 'created_by') THEN
            ALTER TABLE listings ADD COLUMN created_by UUID REFERENCES auth.users(id);
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'listings' AND column_name = 'assigned_to') THEN
            ALTER TABLE listings ADD COLUMN assigned_to UUID REFERENCES auth.users(id);
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'listings' AND column_name = 'team_visibility') THEN
            ALTER TABLE listings ADD COLUMN team_visibility TEXT CHECK (team_visibility IN ('all', 'assigned', 'custom')) DEFAULT 'all';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'listings' AND column_name = 'internal_notes') THEN
            ALTER TABLE listings ADD COLUMN internal_notes JSONB DEFAULT '[]'::jsonb;
        END IF;
    END IF;
END $$;

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_workspaces_status ON workspaces(status);
CREATE INDEX IF NOT EXISTS idx_workspaces_subscription_plan ON workspaces(subscription_plan);
CREATE INDEX IF NOT EXISTS idx_workspaces_trial_ends_at ON workspaces(trial_ends_at) WHERE status = 'trial';

CREATE INDEX IF NOT EXISTS idx_user_profiles_workspace_id ON user_profiles(workspace_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_active ON user_profiles(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_workspace_id ON workspace_invitations(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_email ON workspace_invitations(email);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_token ON workspace_invitations(token);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_expires_at ON workspace_invitations(expires_at);

CREATE INDEX IF NOT EXISTS idx_listings_workspace_id ON listings(workspace_id);
CREATE INDEX IF NOT EXISTS idx_listings_created_by ON listings(created_by);
CREATE INDEX IF NOT EXISTS idx_listings_assigned_to ON listings(assigned_to);
CREATE INDEX IF NOT EXISTS idx_listings_status ON listings(status);
CREATE INDEX IF NOT EXISTS idx_listings_listing_type ON listings(listing_type);
CREATE INDEX IF NOT EXISTS idx_listings_city_state ON listings(city, state);
CREATE INDEX IF NOT EXISTS idx_listings_price ON listings(price);
CREATE INDEX IF NOT EXISTS idx_listings_created_at ON listings(created_at);

-- Create updated_at triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
DROP TRIGGER IF EXISTS update_workspaces_updated_at ON workspaces;
CREATE TRIGGER update_workspaces_updated_at 
    BEFORE UPDATE ON workspaces 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_listings_updated_at ON listings;
CREATE TRIGGER update_listings_updated_at 
    BEFORE UPDATE ON listings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create workspace creation function
CREATE OR REPLACE FUNCTION create_workspace_with_owner(
    p_company_name TEXT,
    p_company_type TEXT DEFAULT 'team',
    p_owner_id UUID DEFAULT NULL,
    p_owner_email TEXT DEFAULT NULL,
    p_owner_first_name TEXT DEFAULT NULL,
    p_owner_last_name TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    workspace_id UUID;
    owner_user_id UUID;
BEGIN
    -- Use provided owner_id or auth.uid()
    owner_user_id := COALESCE(p_owner_id, auth.uid());
    
    -- Create workspace
    INSERT INTO workspaces (company_name, company_type, status)
    VALUES (p_company_name, p_company_type, 'trial')
    RETURNING id INTO workspace_id;
    
    -- Create or update user profile as workspace owner
    INSERT INTO user_profiles (
        id, 
        workspace_id, 
        email, 
        first_name, 
        last_name, 
        role, 
        is_active, 
        joined_at
    ) VALUES (
        owner_user_id,
        workspace_id,
        COALESCE(p_owner_email, (SELECT email FROM auth.users WHERE id = owner_user_id)),
        p_owner_first_name,
        p_owner_last_name,
        'owner',
        true,
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        workspace_id = EXCLUDED.workspace_id,
        role = EXCLUDED.role,
        joined_at = EXCLUDED.joined_at;
    
    RETURN workspace_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create user profile creation trigger
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if user doesn't already have one
    IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE id = NEW.id) THEN
        INSERT INTO user_profiles (id, email, is_active, joined_at)
        VALUES (NEW.id, NEW.email, true, NOW());
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Insert sample data for testing (optional - can be removed in production)
DO $$
DECLARE
    sample_workspace_id UUID;
BEGIN
    -- Only insert sample data if no workspaces exist
    IF NOT EXISTS (SELECT 1 FROM workspaces LIMIT 1) THEN
        -- Create a sample workspace
        INSERT INTO workspaces (
            company_name, 
            company_type, 
            status, 
            onboarding_completed,
            primary_color,
            specialties,
            target_markets
        ) VALUES (
            'Sample Realty Company',
            'team',
            'active',
            true,
            '#3B82F6',
            ARRAY['Residential', 'Commercial'],
            ARRAY['Downtown', 'Suburbs']
        ) RETURNING id INTO sample_workspace_id;
        
        -- Note: User profiles will be created via trigger when actual users sign up
    END IF;
END $$;