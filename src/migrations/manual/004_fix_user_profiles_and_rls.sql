-- Fix user_profiles table structure and apply RLS policies
-- This migration handles the existing table structure and applies RLS

-- First, let's check and fix the user_profiles table structure
DO $
BEGIN
    -- Check if user_profiles has the old structure (with user_id column)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_profiles' 
               AND column_name = 'user_id') THEN
        
        -- Drop existing constraints and indexes that might conflict
        ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_pkey CASCADE;
        DROP INDEX IF EXISTS idx_user_profiles_user_id;
        
        -- Add new columns if they don't exist
        ALTER TABLE user_profiles 
        ADD COLUMN IF NOT EXISTS workspace_id UUID,
        ADD COLUMN IF NOT EXISTS email TEXT,
        ADD COLUMN IF NOT EXISTS first_name TEXT,
        ADD COLUMN IF NOT EXISTS last_name TEXT,
        ADD COLUMN IF NOT EXISTS role TEXT,
        ADD COLUMN IF NOT EXISTS phone TEXT,
        ADD COLUMN IF NOT EXISTS license_number TEXT,
        ADD COLUMN IF NOT EXISTS avatar_url TEXT,
        ADD COLUMN IF NOT EXISTS specialties TEXT[],
        ADD COLUMN IF NOT EXISTS invited_at TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS joined_at TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS invited_by UUID;
        
        -- Update the id column to be UUID and primary key
        -- First, create a new UUID column
        ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS new_id UUID;
        
        -- Update new_id with user_id values (assuming user_id is UUID)
        UPDATE user_profiles SET new_id = user_id WHERE new_id IS NULL;
        
        -- Drop the old id column if it exists and is serial
        ALTER TABLE user_profiles DROP COLUMN IF EXISTS id CASCADE;
        
        -- Rename user_id to id and make it primary key
        ALTER TABLE user_profiles RENAME COLUMN user_id TO id;
        ALTER TABLE user_profiles ADD PRIMARY KEY (id);
        
        -- Drop the temporary new_id column
        ALTER TABLE user_profiles DROP COLUMN IF EXISTS new_id;
        
        -- Add foreign key constraints
        ALTER TABLE user_profiles 
        ADD CONSTRAINT user_profiles_id_fkey 
        FOREIGN KEY (id) REFERENCES auth.users(id);
        
        -- Add workspace foreign key if workspaces table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workspaces') THEN
            ALTER TABLE user_profiles 
            ADD CONSTRAINT user_profiles_workspace_id_fkey 
            FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE;
        END IF;
        
        -- Add invited_by foreign key
        ALTER TABLE user_profiles 
        ADD CONSTRAINT user_profiles_invited_by_fkey 
        FOREIGN KEY (invited_by) REFERENCES auth.users(id);
        
        -- Add role constraint
        ALTER TABLE user_profiles 
        ADD CONSTRAINT user_profiles_role_check 
        CHECK (role IN ('owner', 'admin', 'broker', 'viewer'));
        
        -- Add unique constraint for workspace + email
        ALTER TABLE user_profiles 
        ADD CONSTRAINT unique_workspace_email 
        UNIQUE(workspace_id, email);
        
        RAISE NOTICE 'Fixed user_profiles table structure';
    END IF;
END $;

-- Create workspaces table if it doesn't exist
CREATE TABLE IF NOT EXISTS workspaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_name TEXT NOT NULL,
    company_type TEXT CHECK (company_type IN ('individual', 'team', 'firm')) DEFAULT 'team',
    subscription_plan TEXT CHECK (subscription_plan IN ('trial', 'basic', 'pro', 'enterprise')) DEFAULT 'trial',
    domain TEXT UNIQUE,
    logo_url TEXT,
    primary_color TEXT DEFAULT '#3B82F6',
    address TEXT,
    phone TEXT,
    website TEXT,
    license_number TEXT,
    specialties TEXT[],
    target_markets TEXT[],
    status TEXT CHECK (status IN ('active', 'suspended', 'trial', 'cancelled')) DEFAULT 'trial',
    trial_ends_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '14 days'),
    onboarding_completed BOOLEAN DEFAULT FALSE,
    onboarding_step INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workspace_invitations table if it doesn't exist
CREATE TABLE IF NOT EXISTS workspace_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT CHECK (role IN ('admin', 'broker', 'viewer')) NOT NULL,
    invited_by UUID REFERENCES auth.users(id),
    token TEXT UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(workspace_id, email)
);

-- Create listings table if it doesn't exist
CREATE TABLE IF NOT EXISTS listings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) NOT NULL,
    assigned_to UUID REFERENCES auth.users(id),
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(12,2),
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    property_type TEXT,
    square_footage INTEGER,
    lot_size DECIMAL(10,2),
    year_built INTEGER,
    bedrooms INTEGER,
    bathrooms DECIMAL(3,1),
    status TEXT CHECK (status IN ('draft', 'active', 'pending', 'sold', 'withdrawn')) DEFAULT 'draft',
    listing_type TEXT CHECK (listing_type IN ('sale', 'lease', 'business_sale')) DEFAULT 'sale',
    team_visibility TEXT CHECK (team_visibility IN ('all', 'assigned', 'custom')) DEFAULT 'all',
    internal_notes JSONB DEFAULT '[]'::jsonb,
    photos TEXT[],
    documents TEXT[],
    featured_photo TEXT,
    virtual_tour_url TEXT,
    mls_number TEXT,
    listing_date DATE,
    expiration_date DATE,
    days_on_market INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE listings ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's workspace_id
CREATE OR REPLACE FUNCTION get_user_workspace_id()
RETURNS UUID AS $
BEGIN
    RETURN (
        SELECT workspace_id 
        FROM user_profiles 
        WHERE id = auth.uid()
    );
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has specific role
CREATE OR REPLACE FUNCTION user_has_role(required_role TEXT)
RETURNS BOOLEAN AS $
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_profiles 
        WHERE id = auth.uid() 
        AND role = required_role
    );
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has minimum role level
CREATE OR REPLACE FUNCTION user_has_min_role(min_role TEXT)
RETURNS BOOLEAN AS $
DECLARE
    user_role TEXT;
    role_hierarchy INTEGER;
BEGIN
    -- Get user's current role
    SELECT role INTO user_role 
    FROM user_profiles 
    WHERE id = auth.uid();
    
    -- Define role hierarchy (higher number = more permissions)
    role_hierarchy := CASE user_role
        WHEN 'owner' THEN 4
        WHEN 'admin' THEN 3
        WHEN 'broker' THEN 2
        WHEN 'viewer' THEN 1
        ELSE 0
    END;
    
    -- Check if user meets minimum role requirement
    RETURN role_hierarchy >= CASE min_role
        WHEN 'owner' THEN 4
        WHEN 'admin' THEN 3
        WHEN 'broker' THEN 2
        WHEN 'viewer' THEN 1
        ELSE 0
    END;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "workspace_access_policy" ON workspaces;
DROP POLICY IF EXISTS "workspace_update_policy" ON workspaces;
DROP POLICY IF EXISTS "workspace_create_policy" ON workspaces;
DROP POLICY IF EXISTS "workspace_delete_policy" ON workspaces;

DROP POLICY IF EXISTS "user_profiles_select_policy" ON user_profiles;
DROP POLICY IF EXISTS "user_profiles_update_policy" ON user_profiles;
DROP POLICY IF EXISTS "user_profiles_insert_policy" ON user_profiles;
DROP POLICY IF EXISTS "user_profiles_delete_policy" ON user_profiles;

DROP POLICY IF EXISTS "invitations_select_policy" ON workspace_invitations;
DROP POLICY IF EXISTS "invitations_insert_policy" ON workspace_invitations;
DROP POLICY IF EXISTS "invitations_update_policy" ON workspace_invitations;
DROP POLICY IF EXISTS "invitations_delete_policy" ON workspace_invitations;
DROP POLICY IF EXISTS "invitations_public_token_access" ON workspace_invitations;

DROP POLICY IF EXISTS "listings_select_policy" ON listings;
DROP POLICY IF EXISTS "listings_insert_policy" ON listings;
DROP POLICY IF EXISTS "listings_update_policy" ON listings;
DROP POLICY IF EXISTS "listings_delete_policy" ON listings;

-- WORKSPACES TABLE RLS POLICIES
CREATE POLICY "workspace_access_policy" ON workspaces
    FOR ALL USING (
        id = get_user_workspace_id()
    );

CREATE POLICY "workspace_update_policy" ON workspaces
    FOR UPDATE USING (
        id = get_user_workspace_id() 
        AND user_has_role('owner')
    );

CREATE POLICY "workspace_create_policy" ON workspaces
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "workspace_delete_policy" ON workspaces
    FOR DELETE USING (
        id = get_user_workspace_id() 
        AND user_has_role('owner')
    );

-- USER_PROFILES TABLE RLS POLICIES
CREATE POLICY "user_profiles_select_policy" ON user_profiles
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        OR id = auth.uid()
    );

CREATE POLICY "user_profiles_update_policy" ON user_profiles
    FOR UPDATE USING (
        id = auth.uid()
        OR (
            workspace_id = get_user_workspace_id() 
            AND user_has_min_role('admin')
        )
    );

CREATE POLICY "user_profiles_insert_policy" ON user_profiles
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND (
            user_has_min_role('admin')
            OR id = auth.uid()
        )
    );

CREATE POLICY "user_profiles_delete_policy" ON user_profiles
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
        AND id != auth.uid()
    );

-- WORKSPACE_INVITATIONS TABLE RLS POLICIES
CREATE POLICY "invitations_select_policy" ON workspace_invitations
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

CREATE POLICY "invitations_insert_policy" ON workspace_invitations
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
        AND invited_by = auth.uid()
    );

CREATE POLICY "invitations_update_policy" ON workspace_invitations
    FOR UPDATE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

CREATE POLICY "invitations_delete_policy" ON workspace_invitations
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

CREATE POLICY "invitations_public_token_access" ON workspace_invitations
    FOR SELECT USING (
        token IS NOT NULL
        AND expires_at > NOW()
        AND accepted_at IS NULL
    );

-- LISTINGS TABLE RLS POLICIES
CREATE POLICY "listings_select_policy" ON listings
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        AND (
            team_visibility = 'all'
            OR assigned_to = auth.uid()
            OR created_by = auth.uid()
            OR user_has_min_role('admin')
        )
    );

CREATE POLICY "listings_insert_policy" ON listings
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('broker')
        AND created_by = auth.uid()
    );

CREATE POLICY "listings_update_policy" ON listings
    FOR UPDATE USING (
        workspace_id = get_user_workspace_id()
        AND (
            created_by = auth.uid()
            OR assigned_to = auth.uid()
            OR user_has_min_role('admin')
        )
    );

CREATE POLICY "listings_delete_policy" ON listings
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND (
            created_by = auth.uid()
            OR user_has_min_role('admin')
        )
    );

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_workspace_id() TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_role(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_min_role(TEXT) TO authenticated;

GRANT SELECT, INSERT, UPDATE, DELETE ON workspaces TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON workspace_invitations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON listings TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_workspace_id ON user_profiles(workspace_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_workspace_id ON workspace_invitations(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_token ON workspace_invitations(token);
CREATE INDEX IF NOT EXISTS idx_listings_workspace_id ON listings(workspace_id);
CREATE INDEX IF NOT EXISTS idx_listings_created_by ON listings(created_by);
CREATE INDEX IF NOT EXISTS idx_listings_assigned_to ON listings(assigned_to);

-- Log successful completion
DO $
BEGIN
    RAISE NOTICE 'Row Level Security policies applied successfully';
    RAISE NOTICE 'All tables now have comprehensive RLS policies for workspace data isolation';
END $;