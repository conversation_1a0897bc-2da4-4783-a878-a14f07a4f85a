-- Row Level Security (RLS) Policies Migration
-- This migration implements comprehensive RLS policies for workspace data isolation
-- Requirements: 6.1, 6.2, 6.3, 6.4, 6.5

-- Enable RLS on all tables
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE listings ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's workspace_id
CREATE OR REPLACE FUNCTION get_user_workspace_id()
RETURNS UUID AS $
BEGIN
    RETURN (
        SELECT workspace_id 
        FROM user_profiles 
        WHERE id = auth.uid()
    );
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has specific role
CREATE OR REPLACE FUNCTION user_has_role(required_role TEXT)
RETURNS BOOLEAN AS $
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_profiles 
        WHERE id = auth.uid() 
        AND role = required_role
    );
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has minimum role level
CREATE OR REPLACE FUNCTION user_has_min_role(min_role TEXT)
RETURNS BOOLEAN AS $
DECLARE
    user_role TEXT;
    role_hierarchy INTEGER;
BEGIN
    -- Get user's current role
    SELECT role INTO user_role 
    FROM user_profiles 
    WHERE id = auth.uid();
    
    -- Define role hierarchy (higher number = more permissions)
    role_hierarchy := CASE user_role
        WHEN 'owner' THEN 4
        WHEN 'admin' THEN 3
        WHEN 'broker' THEN 2
        WHEN 'viewer' THEN 1
        ELSE 0
    END;
    
    -- Check if user meets minimum role requirement
    RETURN role_hierarchy >= CASE min_role
        WHEN 'owner' THEN 4
        WHEN 'admin' THEN 3
        WHEN 'broker' THEN 2
        WHEN 'viewer' THEN 1
        ELSE 0
    END;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- WORKSPACES TABLE RLS POLICIES
-- ============================================================================

-- Policy: Users can only access their own workspace
CREATE POLICY "workspace_access_policy" ON workspaces
    FOR ALL USING (
        id = get_user_workspace_id()
    );

-- Policy: Only workspace owners can update workspace settings
CREATE POLICY "workspace_update_policy" ON workspaces
    FOR UPDATE USING (
        id = get_user_workspace_id() 
        AND user_has_role('owner')
    );

-- Policy: Only authenticated users can create workspaces (for signup process)
CREATE POLICY "workspace_create_policy" ON workspaces
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Policy: Only workspace owners can delete workspaces
CREATE POLICY "workspace_delete_policy" ON workspaces
    FOR DELETE USING (
        id = get_user_workspace_id() 
        AND user_has_role('owner')
    );

-- ============================================================================
-- USER_PROFILES TABLE RLS POLICIES
-- ============================================================================

-- Policy: Users can see all team members in their workspace
CREATE POLICY "user_profiles_select_policy" ON user_profiles
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        OR id = auth.uid() -- Users can always see their own profile
    );

-- Policy: Users can update their own profile, owners/admins can update team members
CREATE POLICY "user_profiles_update_policy" ON user_profiles
    FOR UPDATE USING (
        id = auth.uid() -- Users can update their own profile
        OR (
            workspace_id = get_user_workspace_id() 
            AND user_has_min_role('admin') -- Admins and owners can update team members
        )
    );

-- Policy: Only owners and admins can create user profiles (for invitations)
CREATE POLICY "user_profiles_insert_policy" ON user_profiles
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND (
            user_has_min_role('admin') -- Admins and owners can create profiles
            OR id = auth.uid() -- Users can create their own profile during signup
        )
    );

-- Policy: Only owners and admins can delete user profiles
CREATE POLICY "user_profiles_delete_policy" ON user_profiles
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
        AND id != auth.uid() -- Cannot delete own profile
    );

-- ============================================================================
-- WORKSPACE_INVITATIONS TABLE RLS POLICIES
-- ============================================================================

-- Policy: Only workspace owners and admins can view invitations
CREATE POLICY "invitations_select_policy" ON workspace_invitations
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

-- Policy: Only workspace owners and admins can create invitations
CREATE POLICY "invitations_insert_policy" ON workspace_invitations
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
        AND invited_by = auth.uid()
    );

-- Policy: Only workspace owners and admins can update invitations
CREATE POLICY "invitations_update_policy" ON workspace_invitations
    FOR UPDATE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

-- Policy: Only workspace owners and admins can delete invitations
CREATE POLICY "invitations_delete_policy" ON workspace_invitations
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('admin')
    );

-- Special policy: Allow public access to invitations for token validation
CREATE POLICY "invitations_public_token_access" ON workspace_invitations
    FOR SELECT USING (
        token IS NOT NULL
        AND expires_at > NOW()
        AND accepted_at IS NULL
    );

-- ============================================================================
-- LISTINGS TABLE RLS POLICIES
-- ============================================================================

-- Policy: Users can only see listings from their workspace
CREATE POLICY "listings_select_policy" ON listings
    FOR SELECT USING (
        workspace_id = get_user_workspace_id()
        AND (
            team_visibility = 'all' -- All team members can see
            OR assigned_to = auth.uid() -- Assigned user can see
            OR created_by = auth.uid() -- Creator can see
            OR user_has_min_role('admin') -- Admins and owners can see all
        )
    );

-- Policy: Brokers, admins, and owners can create listings
CREATE POLICY "listings_insert_policy" ON listings
    FOR INSERT WITH CHECK (
        workspace_id = get_user_workspace_id()
        AND user_has_min_role('broker') -- Brokers and above can create
        AND created_by = auth.uid()
    );

-- Policy: Users can update listings they created or are assigned to, admins can update all
CREATE POLICY "listings_update_policy" ON listings
    FOR UPDATE USING (
        workspace_id = get_user_workspace_id()
        AND (
            created_by = auth.uid() -- Creator can update
            OR assigned_to = auth.uid() -- Assigned user can update
            OR user_has_min_role('admin') -- Admins and owners can update all
        )
    );

-- Policy: Only creators, assigned users, and admins can delete listings
CREATE POLICY "listings_delete_policy" ON listings
    FOR DELETE USING (
        workspace_id = get_user_workspace_id()
        AND (
            created_by = auth.uid() -- Creator can delete
            OR user_has_min_role('admin') -- Admins and owners can delete
        )
    );

-- ============================================================================
-- SECURITY FUNCTIONS FOR TESTING AND VALIDATION
-- ============================================================================

-- Function to test cross-workspace access (should always return empty)
CREATE OR REPLACE FUNCTION test_cross_workspace_access(target_workspace_id UUID)
RETURNS TABLE(
    table_name TEXT,
    accessible_rows BIGINT,
    should_be_zero BOOLEAN
) AS $
DECLARE
    current_workspace UUID;
BEGIN
    current_workspace := get_user_workspace_id();
    
    -- Test workspaces table
    SELECT 'workspaces', COUNT(*), COUNT(*) = 0
    FROM workspaces 
    WHERE id = target_workspace_id AND id != current_workspace
    INTO table_name, accessible_rows, should_be_zero;
    RETURN NEXT;
    
    -- Test user_profiles table
    SELECT 'user_profiles', COUNT(*), COUNT(*) = 0
    FROM user_profiles 
    WHERE workspace_id = target_workspace_id AND workspace_id != current_workspace
    INTO table_name, accessible_rows, should_be_zero;
    RETURN NEXT;
    
    -- Test workspace_invitations table
    SELECT 'workspace_invitations', COUNT(*), COUNT(*) = 0
    FROM workspace_invitations 
    WHERE workspace_id = target_workspace_id AND workspace_id != current_workspace
    INTO table_name, accessible_rows, should_be_zero;
    RETURN NEXT;
    
    -- Test listings table
    SELECT 'listings', COUNT(*), COUNT(*) = 0
    FROM listings 
    WHERE workspace_id = target_workspace_id AND workspace_id != current_workspace
    INTO table_name, accessible_rows, should_be_zero;
    RETURN NEXT;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate RLS is working correctly
CREATE OR REPLACE FUNCTION validate_rls_policies()
RETURNS TABLE(
    policy_name TEXT,
    table_name TEXT,
    is_enabled BOOLEAN,
    status TEXT
) AS $
BEGIN
    -- Check if RLS is enabled on all required tables
    RETURN QUERY
    SELECT 
        'RLS_ENABLED' as policy_name,
        t.table_name::TEXT,
        t.row_security::BOOLEAN,
        CASE 
            WHEN t.row_security THEN 'ENABLED'
            ELSE 'DISABLED - SECURITY RISK!'
        END as status
    FROM information_schema.tables t
    WHERE t.table_schema = 'public'
    AND t.table_name IN ('workspaces', 'user_profiles', 'workspace_invitations', 'listings')
    ORDER BY t.table_name;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION get_user_workspace_id() TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_role(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_min_role(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION test_cross_workspace_access(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_rls_policies() TO authenticated;

-- Ensure authenticated users can access tables (RLS will control what they see)
GRANT SELECT, INSERT, UPDATE, DELETE ON workspaces TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON workspace_invitations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON listings TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- VALIDATION AND TESTING
-- ============================================================================

-- Validate that RLS is properly enabled
DO $
DECLARE
    rls_check RECORD;
    all_enabled BOOLEAN := TRUE;
BEGIN
    FOR rls_check IN 
        SELECT table_name, row_security 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('workspaces', 'user_profiles', 'workspace_invitations', 'listings')
    LOOP
        IF NOT rls_check.row_security THEN
            RAISE WARNING 'RLS not enabled on table: %', rls_check.table_name;
            all_enabled := FALSE;
        END IF;
    END LOOP;
    
    IF all_enabled THEN
        RAISE NOTICE 'SUCCESS: Row Level Security is enabled on all required tables';
    ELSE
        RAISE EXCEPTION 'FAILED: Row Level Security is not properly configured';
    END IF;
END $;

-- Create a test comment to verify migration completion
COMMENT ON FUNCTION get_user_workspace_id() IS 'RLS helper function - gets current user workspace ID for policy enforcement';
COMMENT ON FUNCTION user_has_role(TEXT) IS 'RLS helper function - checks if user has specific role';
COMMENT ON FUNCTION user_has_min_role(TEXT) IS 'RLS helper function - checks if user meets minimum role requirement';
COMMENT ON FUNCTION test_cross_workspace_access(UUID) IS 'Security testing function - validates cross-workspace access prevention';
COMMENT ON FUNCTION validate_rls_policies() IS 'Validation function - checks RLS policy status';

-- Log successful completion
DO $
BEGIN
    RAISE NOTICE 'Row Level Security policies migration completed successfully';
    RAISE NOTICE 'All tables now have comprehensive RLS policies for workspace data isolation';
    RAISE NOTICE 'Cross-workspace data access is prevented by design';
END $;