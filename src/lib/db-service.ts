import { sql, SQL } from 'drizzle-orm'
import { getDatabase } from '../database'
import { supabaseClient } from './supabase'
import config from '../config'

export interface QueryResult<T = any> {
  data: T[]
  error?: string
  rowCount?: number
  executionTime?: number
}

export class DatabaseService {
  private db = getDatabase()

  /**
   * Execute raw SQL using Drizzle/Postgres connection
   * This gives you direct access to the Postgres database
   * Note: For parameterized queries, embed parameters directly in the query string
   * or use executeParameterizedSQL for better safety
   */
  async executeRawSQL<T = any>(query: string): Promise<QueryResult<T>> {
    const startTime = Date.now()
    
    try {
      console.log('🔍 Executing SQL query:', query)

      let result: any[]
      
      // For parameterized queries, users should pass the query with $1, $2, etc. placeholders
      // and provide parameters separately. sql.raw() doesn't support parameter binding.
      // For true parameterized queries, use the sql template literal instead.
      result = await this.db.execute(sql.raw(query))

      const executionTime = Date.now() - startTime
      
      console.log(`✅ Query executed successfully in ${executionTime}ms`)
      console.log(`📈 Returned ${result.length} rows`)

      return {
        data: result,
        rowCount: result.length,
        executionTime
      }
    } catch (error) {
      const executionTime = Date.now() - startTime
      console.error('❌ SQL execution failed:', error)
      
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      }
    }
  }

  /**
   * Execute raw SQL using Supabase client
   * This uses Supabase's RPC functionality and respects RLS policies
   */
  async executeSupabaseSQL<T = any>(query: string): Promise<QueryResult<T>> {
    const startTime = Date.now()
    
    try {
      console.log('🔍 Executing Supabase SQL query:', query)

      const { data, error } = await supabaseClient.rpc('execute_sql', {
        sql_query: query
      })

      const executionTime = Date.now() - startTime

      if (error) {
        console.error('❌ Supabase SQL execution failed:', error)
        return {
          data: [],
          error: error.message,
          executionTime
        }
      }

      console.log(`✅ Supabase query executed successfully in ${executionTime}ms`)
      console.log(`📈 Returned ${Array.isArray(data) ? data.length : 'unknown'} rows`)

      return {
        data: Array.isArray(data) ? data : [data],
        rowCount: Array.isArray(data) ? data.length : 1,
        executionTime
      }
    } catch (error) {
      const executionTime = Date.now() - startTime
      console.error('❌ Supabase SQL execution failed:', error)
      
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      }
    }
  }

  /**
   * Execute raw SQL with proper parameterization using Drizzle's sql template
   * This provides better type safety and SQL injection protection
   */
  async executeParameterizedSQL<T = any>(sqlTemplate: SQL): Promise<QueryResult<T>> {
    const startTime = Date.now()
    
    try {
      console.log('🔍 Executing parameterized SQL query')

      const result = await this.db.execute(sqlTemplate)
      const executionTime = Date.now() - startTime
      
      console.log(`✅ Parameterized query executed successfully in ${executionTime}ms`)
      console.log(`📈 Returned ${result.length} rows`)

      return {
        data: result as T[],
        rowCount: result.length,
        executionTime
      }
    } catch (error) {
      const executionTime = Date.now() - startTime
      console.error('❌ Parameterized SQL execution failed:', error)
      
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      }
    }
  }

  /**
   * Execute a SELECT query and return results
   */
  async select<T = any>(query: string): Promise<QueryResult<T>> {
    if (!query.trim().toLowerCase().startsWith('select')) {
      return {
        data: [],
        error: 'Only SELECT queries are allowed with this method'
      }
    }
    
    return this.executeRawSQL<T>(query)
  }

  /**
   * Execute an INSERT, UPDATE, or DELETE query
   */
  async mutate<T = any>(query: string): Promise<QueryResult<T>> {
    const trimmedQuery = query.trim().toLowerCase()
    const allowedOperations = ['insert', 'update', 'delete']
    
    if (!allowedOperations.some(op => trimmedQuery.startsWith(op))) {
      return {
        data: [],
        error: 'Only INSERT, UPDATE, and DELETE queries are allowed with this method'
      }
    }
    
    return this.executeRawSQL<T>(query)
  }

  /**
   * Execute a transaction with multiple queries
   */
  async transaction(queries: Array<{ sql: string }>): Promise<QueryResult[]> {
    const results: QueryResult[] = []
    
    try {
      // Note: For proper transactions, you'd want to use db.transaction()
      // but this provides a simple sequential execution
      for (const query of queries) {
        const result = await this.executeRawSQL(query.sql)
        results.push(result)
        
        if (result.error) {
          throw new Error(`Transaction failed at query: ${query.sql}. Error: ${result.error}`)
        }
      }
      
      console.log(`✅ Transaction completed successfully with ${queries.length} queries`)
      return results
    } catch (error) {
      console.error('❌ Transaction failed:', error)
      // In a real transaction, you'd rollback here
      throw error
    }
  }

  /**
   * Health check - test database connectivity
   */
  async healthCheck(): Promise<{ healthy: boolean; message: string; latency?: number }> {
    try {
      const result = await this.select('SELECT 1 as test')
      
      if (result.error) {
        return {
          healthy: false,
          message: `Database error: ${result.error}`
        }
      }
      
      return {
        healthy: true,
        message: 'Database connection is healthy',
        latency: result.executionTime
      }
    } catch (error) {
      return {
        healthy: false,
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get table information
   */
  async getTableInfo(tableName: string): Promise<QueryResult> {
    const query = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = '${tableName}'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `
    
    return this.select(query)
  }

  /**
   * List all tables in the public schema
   */
  async listTables(): Promise<QueryResult> {
    const query = `
      SELECT 
        table_name,
        table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `
    
    return this.select(query)
  }
}

// Export a singleton instance
export const dbService = new DatabaseService()

export default dbService 