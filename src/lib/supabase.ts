import { createClient } from '@supabase/supabase-js'
import config from '../config'

if (!config.supabase.url || !config.supabase.serviceKey) {
  throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables')
}

export const supabaseClient = createClient(
  config.supabase.url, 
  config.supabase.serviceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

