#!/usr/bin/env bun
import { sql } from 'drizzle-orm'
import { dbService } from './db-service'

// Example usage of the database service
async function exampleUsage() {
  console.log('🔧 Database Service Examples')
  console.log('===========================')

  try {
    // 1. Health Check
    console.log('\n1. Health Check:')
    const health = await dbService.healthCheck()
    console.log('Health:', health)

    // 2. List all tables
    console.log('\n2. List Tables:')
    const tables = await dbService.listTables()
    console.log('Tables found:', tables.data?.length || 0)
    tables.data?.forEach((table: any) => {
      console.log(`  - ${table.table_name} (${table.table_type})`)
    })

    // 3. Get table information for a specific table
    if (tables.data && tables.data.length > 0) {
      const firstTable = tables.data[0] as any
      console.log(`\n3. Table Info for '${firstTable.table_name}':`)
      const tableInfo = await dbService.getTableInfo(firstTable.table_name)
      if (tableInfo.data) {
        tableInfo.data.forEach((column: any) => {
          console.log(`  - ${column.column_name}: ${column.data_type}`)
        })
      }
    }

    // 4. Raw SQL execution examples
    console.log('\n4. Raw SQL Examples:')

    // Simple SELECT
    const currentTime = await dbService.select('SELECT NOW() as current_time')
    console.log('Current time:', currentTime.data?.[0])

    // Count records in a table (if any exist)
    if (tables.data && tables.data.length > 0) {
      const firstTable = tables.data[0] as any
      const count = await dbService.select(`SELECT COUNT(*) as count FROM ${firstTable.table_name}`)
      console.log(`Records in ${firstTable.table_name}:`, count.data?.[0])
    }

    // 5. Parameterized SQL example (safer way)
    console.log('\n5. Parameterized SQL Example:')
    const userQuery = await dbService.executeParameterizedSQL(
      sql`SELECT table_name, table_type FROM information_schema.tables WHERE table_schema = ${'public'} LIMIT ${3}`
    )
    console.log('Parameterized query result:', userQuery.data?.length, 'rows')

    // 6. Example of a simple transaction
    console.log('\n6. Transaction Example:')
    const transactionResult = await dbService.transaction([
      { sql: 'SELECT 1 as test1' },
      { sql: 'SELECT 2 as test2' },
      { sql: 'SELECT NOW() as test3' }
    ])
    console.log('Transaction completed with', transactionResult.length, 'queries')

    // 7. Supabase SQL execution (if you have the execute_sql function set up)
    console.log('\n7. Supabase SQL (may fail if RPC function not set up):')
    try {
      const supabaseResult = await dbService.executeSupabaseSQL('SELECT 1 as test')
      console.log('Supabase result:', supabaseResult)
    } catch (error) {
      console.log('Supabase execution failed (expected if execute_sql RPC not set up):', error)
    }

  } catch (error) {
    console.error('❌ Example failed:', error)
  }
}

// Helper function to demonstrate different query patterns
async function demoQueryPatterns() {
  console.log('\n🎯 Query Pattern Examples')
  console.log('=========================')

  // Pattern 1: Simple data retrieval
  const simpleQuery = await dbService.select(`
    SELECT 
      schemaname, 
      tablename, 
      tableowner 
    FROM pg_tables 
    WHERE schemaname = 'public'
    LIMIT 5
  `)
  
  console.log('1. Simple query:', simpleQuery.data?.length, 'tables')

  // Pattern 2: Complex aggregation
  const aggregationQuery = await dbService.select(`
    SELECT 
      schemaname,
      COUNT(*) as table_count
    FROM pg_tables 
    GROUP BY schemaname
    ORDER BY table_count DESC
  `)
  
  console.log('2. Aggregation query:', aggregationQuery.data?.length, 'schemas')

  // Pattern 3: Using parameterized query for user input safety
  const safeQuery = await dbService.executeParameterizedSQL(
    sql`
      SELECT table_name, column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = ${'public'} 
      AND data_type = ${'text'}
      LIMIT ${10}
    `
  )
  
  console.log('3. Safe parameterized query:', safeQuery.data?.length, 'text columns')

  // Pattern 4: Database maintenance queries
  const maintenanceInfo = await dbService.select(`
    SELECT 
      pg_size_pretty(pg_database_size(current_database())) as database_size,
      current_database() as database_name,
      version() as postgres_version
  `)
  
  console.log('4. Database info:', maintenanceInfo.data?.[0])
}

// Export for use in other files
export { dbService, exampleUsage, demoQueryPatterns }

// Run examples if this file is executed directly
if (import.meta.main) {
  exampleUsage()
    .then(() => demoQueryPatterns())
    .then(() => {
      console.log('\n✅ All examples completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Examples failed:', error)
      process.exit(1)
    })
} 