import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./schema";

// Database connection factory
export function createDatabase(connectionString: string) {
  const client = postgres(connectionString, {
    max: 10,
    idle_timeout: 20,
    connect_timeout: 10,
  });

  return drizzle(client, { schema });
}

// Default database instance (can be overridden)
let defaultDb: ReturnType<typeof createDatabase> | null = null;

export function getDatabase() {
  if (!defaultDb) {
    const connectionString =
      process.env.DATABASE_URL || process.env.POSTGRES_URL;
    if (!connectionString) {
      throw new Error(
        "DATABASE_URL or POSTGRES_URL environment variable is required"
      );
    }
    defaultDb = createDatabase(connectionString);
  }
  return defaultDb;
}

// Export schema for convenience
export { schema };

// Default database instance for convenience
export const db = getDatabase();

// Type helpers
export type Database = ReturnType<typeof createDatabase>;
export type Schema = typeof schema;
