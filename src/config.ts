// Environment configuration
export const config = {
  // Server configuration
  port: parseInt(process.env.PORT || '3001'),
  
  // Database configuration
  database: {
    url: process.env.DATABASE_URL || process.env.POSTGRES_URL,
  },
  
  // Supabase configuration
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_KEY,
  },
  
  // Redis configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
  },
  
  // CORS configuration
  cors: {
    origins: [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:3001'
    ],
  },
  
  // Environment
  isDevelopment: process.env.NODE_ENV !== 'production',
  isProduction: process.env.NODE_ENV === 'production',
}

// Validate required environment variables
export function validateConfig() {
  const required = [
    'DATABASE_URL',
    'SUPABASE_URL',
    'SUPABASE_SERVICE_KEY'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

export default config