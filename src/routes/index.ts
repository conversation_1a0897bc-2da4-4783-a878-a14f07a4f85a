import { Hono } from 'hono'
import health from './health'
import api from './api'
import users from './users'
import dev from './dev'
import config from '../config'

const routes = new Hono()

// Mount all route modules
routes.route('/health', health)
routes.route('/api', api)
routes.route('/api/users', users)

// Mount dev routes only in development
if (config.isDevelopment) {
  routes.route('/dev', dev)
}

export default routes