import { <PERSON>o } from "hono";
import { db, schema } from "../database";
import { supabaseClient } from "../lib/supabase";
import { sql } from "drizzle-orm";

const api = new Hono();

// Basic test endpoint
api.get("/test", (c) => {
  return c.json({ message: "Backend API is running!" });
});

// Database test endpoint
api.get("/test-db", async (c) => {
  try {
    // Test basic queries
    const basicTest = await db.execute(
      sql`SELECT NOW() as current_time, version() as pg_version`
    );

    // Test if our tables exist
    const tableCheck = await db.execute(sql`
      SELECT table_name, column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN ('health_checks', 'workspaces', 'user_profiles', 'listings')
      ORDER BY table_name, ordinal_position
    `);

    return c.json({
      success: true,
      database_info: {
        current_time: basicTest[0]?.current_time,
        postgres_version: basicTest[0]?.pg_version,
        raw_basic_test: basicTest[0],
      },
      schema_info: {
        tables_found: tableCheck,
        table_count: tableCheck.length,
      },
      message: "Database connection and Drizzle ORM working correctly!",
    });
  } catch (error) {
    return c.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown database error",
        message: "Database test failed",
      },
      500
    );
  }
});

// Supabase test endpoint
api.get("/test-supabase", async (c) => {
  try {
    // Test Supabase client
    const { data: authUsers, error: authError } =
      await supabaseClient.auth.admin.listUsers();
    if (authError) throw authError;

    // Test a simple query through Supabase
    const { data: tables, error: tablesError } = await supabaseClient
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_schema", "public")
      .limit(10);

    return c.json({
      success: true,
      auth_info: {
        user_count: authUsers.users.length,
        users: authUsers.users.map((u) => ({
          id: u.id,
          email: u.email,
          created_at: u.created_at,
        })),
      },
      database_info: {
        tables_accessible: !tablesError,
        tables_error: tablesError?.message,
        sample_tables: tables?.slice(0, 5),
      },
      message: "Supabase connection working correctly!",
    });
  } catch (error) {
    return c.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown Supabase error",
        message: "Supabase test failed",
      },
      500
    );
  }
});

// Comprehensive integration test
api.get("/test-integration", async (c) => {
  const results = {
    success: true,
    tests: {} as Record<string, any>,
    summary: {
      passed: 0,
      failed: 0,
      total: 0,
    },
  };

  const runTest = async (name: string, testFn: () => Promise<any>) => {
    results.summary.total++;
    try {
      const result = await testFn();
      results.tests[name] = { status: "passed", result };
      results.summary.passed++;
    } catch (error) {
      results.tests[name] = {
        status: "failed",
        error: error instanceof Error ? error.message : "unknown",
      };
      results.summary.failed++;
      results.success = false;
    }
  };



  // Test 2: Supabase basic connection
  await runTest("supabase_connection", async () => {
    const { data, error } = await supabaseClient
      .from("information_schema.tables")
      .select("count")
      .eq("table_schema", "public")
      .limit(1);

    if (error) throw new Error(error.message);
    return { accessible: true, query_works: true };
  });

  // Test 3: Create workspace via Drizzle
  await runTest("drizzle_workspace_test", async () => {
    const workspaces = await db.select().from(schema.workspaces).limit(1);
    return { count: workspaces.length, message: "Workspace table accessible" };
  });

  // Test 5: Create workspace via Drizzle
  await runTest("drizzle_workspace_insert", async () => {
    const [newWorkspace] = await db
      .insert(schema.workspaces)
      .values({
        companyName: `Test Company ${Date.now()}`,
        companyType: "team",
        status: "trial",
      })
      .returning();
    return newWorkspace;
  });

  return c.json(results, results.success ? 200 : 500);
});

export default api;
