import { Hono } from 'hono'
import { db, schema } from '../database'

const users = new Hono()

// Get all user profiles with auth user data
users.get('/', async (c) => {
  try {
    const userProfiles = await db
      .select({
        id: schema.userProfiles.id,
        userId: schema.userProfiles.userId,
        displayName: schema.userProfiles.displayName,
        bio: schema.userProfiles.bio,
        isActive: schema.userProfiles.isActive,
        createdAt: schema.userProfiles.createdAt,
        updatedAt: schema.userProfiles.updatedAt,
        email: schema.authUsers.email,
      })
      .from(schema.userProfiles)
      .leftJoin(schema.authUsers, schema.eq(schema.userProfiles.userId, schema.authUsers.id))
      .orderBy(schema.sql`${schema.userProfiles.createdAt} DESC`)
      .limit(20)

    return c.json({ success: true, data: userProfiles })
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

// Create a new user profile (requires existing auth user)
users.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { userId, displayName, bio, isActive } = body

    if (!userId) {
      return c.json({ success: false, error: 'User ID is required' }, 400)
    }

    const [newProfile] = await db.insert(schema.userProfiles).values({
      userId,
      displayName: displayName || null,
      bio: bio || null,
      isActive: isActive !== undefined ? isActive : true
    }).returning()

    return c.json({ success: true, data: newProfile }, 201)
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

// Get a specific user profile by profile ID
users.get('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'))
    if (isNaN(id)) {
      return c.json({ success: false, error: 'Invalid profile ID' }, 400)
    }

    const [userProfile] = await db
      .select({
        id: schema.userProfiles.id,
        userId: schema.userProfiles.userId,
        displayName: schema.userProfiles.displayName,
        bio: schema.userProfiles.bio,
        isActive: schema.userProfiles.isActive,
        createdAt: schema.userProfiles.createdAt,
        updatedAt: schema.userProfiles.updatedAt,
        email: schema.authUsers.email,
      })
      .from(schema.userProfiles)
      .leftJoin(schema.authUsers, schema.eq(schema.userProfiles.userId, schema.authUsers.id))
      .where(schema.eq(schema.userProfiles.id, id))
    
    if (!userProfile) {
      return c.json({ success: false, error: 'User profile not found' }, 404)
    }

    return c.json({ success: true, data: userProfile })
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

// Get user profile by auth user ID
users.get('/by-user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId')

    const [userProfile] = await db
      .select({
        id: schema.userProfiles.id,
        userId: schema.userProfiles.userId,
        displayName: schema.userProfiles.displayName,
        bio: schema.userProfiles.bio,
        isActive: schema.userProfiles.isActive,
        createdAt: schema.userProfiles.createdAt,
        updatedAt: schema.userProfiles.updatedAt,
        email: schema.authUsers.email,
      })
      .from(schema.userProfiles)
      .leftJoin(schema.authUsers, schema.eq(schema.userProfiles.userId, schema.authUsers.id))
      .where(schema.eq(schema.userProfiles.userId, userId))
    
    if (!userProfile) {
      return c.json({ success: false, error: 'User profile not found' }, 404)
    }

    return c.json({ success: true, data: userProfile })
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

// Update a user profile
users.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'))
    if (isNaN(id)) {
      return c.json({ success: false, error: 'Invalid profile ID' }, 400)
    }

    const body = await c.req.json()
    const { displayName, bio, isActive } = body

    const [updatedProfile] = await db.update(schema.userProfiles)
      .set({ 
        displayName: displayName !== undefined ? displayName : undefined,
        bio: bio !== undefined ? bio : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
        updatedAt: new Date()
      })
      .where(schema.eq(schema.userProfiles.id, id))
      .returning()

    if (!updatedProfile) {
      return c.json({ success: false, error: 'User profile not found' }, 404)
    }

    return c.json({ success: true, data: updatedProfile })
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

// Delete a user profile (doesn't delete auth user)
users.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'))
    if (isNaN(id)) {
      return c.json({ success: false, error: 'Invalid profile ID' }, 400)
    }

    const [deletedProfile] = await db.delete(schema.userProfiles)
      .where(schema.eq(schema.userProfiles.id, id))
      .returning()

    if (!deletedProfile) {
      return c.json({ success: false, error: 'User profile not found' }, 404)
    }

    return c.json({ success: true, data: deletedProfile })
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'unknown' 
    }, 500)
  }
})

export default users