import { Hono } from 'hono'
import { db, schema } from '../database'
import { supabaseClient } from '../lib/supabase'
import { cacheService } from '../lib/cache'

const health = new Hono()

// Health check endpoint
health.get('/', async (c) => {
  const results = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {} as Record<string, string>,
    tests: {} as Record<string, any>
  }

  try {

    // Test 2: Supabase auth connection
    try {
      const { data: authUsers, error: authError } = await supabaseClient.auth.admin.listUsers()
      if (authError) throw authError
      results.services.supabase_auth = `connected (${authUsers.users.length} users)`
    } catch (error) {
      results.services.supabase_auth = `error: ${error instanceof Error ? error.message : 'unknown'}`
      results.status = 'unhealthy'
    }

    // Test 3: Redis connection
    try {
      await cacheService.set('health-check', 'ok', 10)
      const cacheTest = await cacheService.get('health-check')
      results.services.redis = cacheTest === 'ok' ? 'connected' : 'error'
    } catch (error) {
      results.services.redis = `error: ${error instanceof Error ? error.message : 'unknown'}`
      results.status = 'unhealthy'
    }


    return c.json(results, results.status === 'healthy' ? 200 : 500)
  } catch (error) {
    return c.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

export default health