import { Hono } from 'hono'
import { db } from '../database'
import { 
  workspaces, 
  userProfiles, 
  listings,
  type NewWorkspace,
  type NewUserProfile,
  type NewListing
} from '../schema'
import { eq } from 'drizzle-orm'
import config from '../config'

const dev = new Hono()

// Middleware to only allow dev routes in development
dev.use('*', async (c, next) => {
  if (!config.isDevelopment) {
    return c.json({ error: 'Dev routes are only available in development environment' }, 403)
  }
  await next()
})

// Dev route overview
dev.get('/', (c) => {
  return c.json({
    message: 'Development API Routes',
    environment: 'development',
    endpoints: {
      seed: {
        all: 'POST /dev/seed - Seed all test data',
        workspaces: 'POST /dev/seed/workspaces - Seed workspace data',
        users: 'POST /dev/seed/users - Seed user profiles',
        listings: 'POST /dev/seed/listings - Seed listing data',
      },
      test: {
        db: 'GET /dev/test/db - Test database connection',
        data: 'GET /dev/test/data - View seeded data',
      },
      cleanup: {
        all: 'DELETE /dev/cleanup - Clean all test data',
        workspaces: 'DELETE /dev/cleanup/workspaces - Clean workspace data',
        users: 'DELETE /dev/cleanup/users - Clean user profiles',
        listings: 'DELETE /dev/cleanup/listings - Clean listing data',
      }
    }
  })
})

// Test database connection
dev.get('/test/db', async (c) => {
  try {
    // Simple test by selecting from workspaces table
    const result = await db.select().from(workspaces).limit(1)
    
    return c.json({
      status: 'success',
      message: 'Database connection working',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return c.json({
      status: 'error',
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500)
  }
})

// View seeded data
dev.get('/test/data', async (c) => {
  try {
    const [workspaceData, userData, listingData] = await Promise.all([
      db.select().from(workspaces).limit(5),
      db.select().from(userProfiles).limit(5),
      db.select().from(listings).limit(5)
    ])

    return c.json({
      status: 'success',
      data: {
        workspaces: {
          count: workspaceData.length,
          sample: workspaceData
        },
        users: {
          count: userData.length,
          sample: userData
        },
        listings: {
          count: listingData.length,
          sample: listingData
        }
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return c.json({
      status: 'error',
      message: 'Failed to fetch test data',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})


// Helper functions for seeding data
async function seedWorkspaces() {
  const testWorkspaces: NewWorkspace[] = [
    {
      companyName: 'Acme Real Estate',
      companyType: 'firm',
      subscriptionPlan: 'pro',
      domain: 'acme-realty.dev',
      address: '123 Main St, Anytown, CA 90210',
      phone: '(*************',
      website: 'https://acme-realty.dev',
      licenseNumber: 'CA-RE-12345',
      specialties: ['residential', 'commercial'],
      targetMarkets: ['luxury', 'first-time-buyers'],
      status: 'active'
    },
    {
      companyName: 'Solo Broker LLC',
      companyType: 'individual',
      subscriptionPlan: 'basic',
      domain: 'solo-broker.dev',
      address: '456 Oak Ave, Somewhere, TX 75001',
      phone: '(*************',
      licenseNumber: 'TX-RE-67890',
      specialties: ['residential'],
      targetMarkets: ['first-time-buyers'],
      status: 'trial'
    },
    {
      companyName: 'Team Realty Group',
      companyType: 'team',
      subscriptionPlan: 'enterprise',
      domain: 'team-realty.dev',
      address: '789 Pine Blvd, Elsewhere, FL 33101',
      phone: '(*************',
      website: 'https://team-realty.dev',
      licenseNumber: 'FL-RE-11111',
      specialties: ['residential', 'investment'],
      targetMarkets: ['luxury', 'investment'],
      status: 'active'
    }
  ]

  const result = await db.insert(workspaces).values(testWorkspaces).returning()
  return { count: result.length, workspaces: result }
}


export default dev