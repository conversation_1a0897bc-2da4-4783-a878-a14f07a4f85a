import { pgTable, index, unique, pgPolicy, check, uuid, text, timestamp, boolean, integer, foreignKey, numeric, jsonb, date, pgSchema } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"
import { authenticatedRole, anonRole } from "drizzle-orm/supabase"

// ============================================================================
// AUTH SCHEMA REFERENCE
// ============================================================================

const authSchema = pgSchema('auth');
export const authUsers = authSchema.table('users', {
	id: uuid().primaryKey().notNull(),
});

// ============================================================================
// CORE TABLES
// ============================================================================

export const workspaces = pgTable("workspaces", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	companyName: text("company_name").notNull(),
	companyType: text("company_type").default('team'),
	subscriptionPlan: text("subscription_plan").default('trial'),
	domain: text(),
	logoUrl: text("logo_url"),
	primaryColor: text("primary_color").default('#3B82F6'),
	address: text(),
	phone: text(),
	website: text(),
	licenseNumber: text("license_number"),
	specialties: text().array(),
	targetMarkets: text("target_markets").array(),
	status: text().default('trial'),
	trialEndsAt: timestamp("trial_ends_at", { withTimezone: true, mode: 'string' }).default(sql`(now() + '14 days'::interval)`),
	onboardingCompleted: boolean("onboarding_completed").default(false),
	onboardingStep: integer("onboarding_step").default(1),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	// Indexes
	index("idx_workspaces_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("idx_workspaces_subscription_plan").using("btree", table.subscriptionPlan.asc().nullsLast().op("text_ops")),
	index("idx_workspaces_trial_ends_at").using("btree", table.trialEndsAt.asc().nullsLast().op("timestamptz_ops")).where(sql`(status = 'trial'::text)`),
	
	// Constraints
	unique("workspaces_domain_key").on(table.domain),
	check("workspaces_company_type_check", sql`company_type = ANY (ARRAY['individual'::text, 'team'::text, 'firm'::text])`),
	check("workspaces_subscription_plan_check", sql`subscription_plan = ANY (ARRAY['trial'::text, 'basic'::text, 'pro'::text, 'enterprise'::text])`),
	check("workspaces_status_check", sql`status = ANY (ARRAY['active'::text, 'suspended'::text, 'trial'::text, 'cancelled'::text])`),
	
	// RLS Policies
	pgPolicy("workspace_access_policy", { 
		as: "permissive", 
		for: "select", 
		to: [authenticatedRole], 
		using: sql`id = get_user_workspace_id()` 
	}),
	pgPolicy("workspace_update_policy", { 
		as: "permissive", 
		for: "update", 
		to: [authenticatedRole], 
		using: sql`id = get_user_workspace_id() AND user_has_role('owner')` 
	}),
	pgPolicy("workspace_create_policy", { 
		as: "permissive", 
		for: "insert", 
		to: [authenticatedRole], 
		withCheck: sql`auth.uid() IS NOT NULL` 
	}),
	pgPolicy("workspace_delete_policy", { 
		as: "permissive", 
		for: "delete", 
		to: [authenticatedRole], 
		using: sql`id = get_user_workspace_id() AND user_has_role('owner')` 
	}),
]);

export const userProfiles = pgTable("user_profiles", {
	id: uuid().primaryKey().notNull(),
	displayName: text("display_name"),
	bio: text(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	workspaceId: uuid("workspace_id"),
	email: text(),
	firstName: text("first_name"),
	lastName: text("last_name"),
	role: text(),
	phone: text(),
	licenseNumber: text("license_number"),
	avatarUrl: text("avatar_url"),
	specialties: text().array(),
	invitedAt: timestamp("invited_at", { withTimezone: true, mode: 'string' }),
	joinedAt: timestamp("joined_at", { withTimezone: true, mode: 'string' }),
	invitedBy: uuid("invited_by"),
	userId: uuid("user_id"),
}, (table) => [
	// Indexes
	index("idx_user_profiles_active").using("btree", table.isActive.asc().nullsLast().op("bool_ops")).where(sql`(is_active = true)`),
	index("idx_user_profiles_email").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("idx_user_profiles_role").using("btree", table.role.asc().nullsLast().op("text_ops")),
	index("idx_user_profiles_user_id").using("btree", table.id.asc().nullsLast().op("uuid_ops")),
	index("idx_user_profiles_workspace_id").using("btree", table.workspaceId.asc().nullsLast().op("uuid_ops")),
	
	// Foreign Keys
	foreignKey({
		columns: [table.workspaceId],
		foreignColumns: [workspaces.id],
		name: "user_profiles_workspace_id_fkey"
	}).onDelete("cascade"),
	foreignKey({
		columns: [table.userId],
		foreignColumns: [authUsers.id],
		name: "user_profiles_user_id_fkey"
	}).onDelete("cascade"),
	
	// Constraints
	unique("unique_workspace_email").on(table.workspaceId, table.email),
	check("user_profiles_role_check", sql`role = ANY (ARRAY['owner'::text, 'admin'::text, 'broker'::text, 'viewer'::text])`),
	
	// RLS Policies
	pgPolicy("user_profiles_select_policy", { 
		as: "permissive", 
		for: "select", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() OR id = auth.uid()` 
	}),
	pgPolicy("user_profiles_update_policy", { 
		as: "permissive", 
		for: "update", 
		to: [authenticatedRole], 
		using: sql`id = auth.uid() OR (workspace_id = get_user_workspace_id() AND user_has_min_role('admin'))` 
	}),
	pgPolicy("user_profiles_insert_policy", { 
		as: "permissive", 
		for: "insert", 
		to: [authenticatedRole], 
		withCheck: sql`workspace_id = get_user_workspace_id() AND (user_has_min_role('admin') OR id = auth.uid())` 
	}),
	pgPolicy("user_profiles_delete_policy", { 
		as: "permissive", 
		for: "delete", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND id != auth.uid()` 
	}),
]);

export const workspaceInvitations = pgTable("workspace_invitations", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	workspaceId: uuid("workspace_id"),
	email: text().notNull(),
	role: text().notNull(),
	invitedBy: uuid("invited_by"),
	token: text().notNull(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }).notNull(),
	acceptedAt: timestamp("accepted_at", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	// Indexes
	index("idx_workspace_invitations_email").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("idx_workspace_invitations_expires_at").using("btree", table.expiresAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_workspace_invitations_token").using("btree", table.token.asc().nullsLast().op("text_ops")),
	index("idx_workspace_invitations_workspace_id").using("btree", table.workspaceId.asc().nullsLast().op("uuid_ops")),
	
	// Foreign Keys
	foreignKey({
		columns: [table.workspaceId],
		foreignColumns: [workspaces.id],
		name: "workspace_invitations_workspace_id_fkey"
	}).onDelete("cascade"),
	foreignKey({
		columns: [table.invitedBy],
		foreignColumns: [authUsers.id],
		name: "workspace_invitations_invited_by_fkey"
	}),
	
	// Constraints
	unique("workspace_invitations_workspace_id_email_key").on(table.workspaceId, table.email),
	unique("workspace_invitations_token_key").on(table.token),
	check("workspace_invitations_role_check", sql`role = ANY (ARRAY['admin'::text, 'broker'::text, 'viewer'::text])`),
	
	// RLS Policies
	pgPolicy("invitations_select_policy", { 
		as: "permissive", 
		for: "select", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('admin')` 
	}),
	pgPolicy("invitations_insert_policy", { 
		as: "permissive", 
		for: "insert", 
		to: [authenticatedRole], 
		withCheck: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('admin') AND invited_by = auth.uid()` 
	}),
	pgPolicy("invitations_update_policy", { 
		as: "permissive", 
		for: "update", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('admin')` 
	}),
	pgPolicy("invitations_delete_policy", { 
		as: "permissive", 
		for: "delete", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('admin')` 
	}),
	pgPolicy("invitations_public_token_access", { 
		as: "permissive", 
		for: "select", 
		to: [anonRole, authenticatedRole], 
		using: sql`token IS NOT NULL AND expires_at > NOW() AND accepted_at IS NULL` 
	}),
]);

export const listings = pgTable("listings", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	workspaceId: uuid("workspace_id").notNull(),
	createdBy: uuid("created_by").notNull(),
	assignedTo: uuid("assigned_to"),
	title: text().notNull(),
	description: text(),
	price: numeric({ precision: 12, scale: 2 }),
	address: text(),
	city: text(),
	state: text(),
	zipCode: text("zip_code"),
	propertyType: text("property_type"),
	squareFootage: integer("square_footage"),
	lotSize: numeric("lot_size", { precision: 10, scale: 2 }),
	yearBuilt: integer("year_built"),
	bedrooms: integer(),
	bathrooms: numeric({ precision: 3, scale: 1 }),
	status: text().default('draft'),
	listingType: text("listing_type").default('sale'),
	teamVisibility: text("team_visibility").default('all'),
	internalNotes: jsonb("internal_notes").default([]),
	photos: text().array(),
	documents: text().array(),
	featuredPhoto: text("featured_photo"),
	virtualTourUrl: text("virtual_tour_url"),
	mlsNumber: text("mls_number"),
	listingDate: date("listing_date"),
	expirationDate: date("expiration_date"),
	daysOnMarket: integer("days_on_market"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	// Indexes
	index("idx_listings_assigned_to").using("btree", table.assignedTo.asc().nullsLast().op("uuid_ops")),
	index("idx_listings_city_state").using("btree", table.city.asc().nullsLast().op("text_ops"), table.state.asc().nullsLast().op("text_ops")),
	index("idx_listings_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_listings_created_by").using("btree", table.createdBy.asc().nullsLast().op("uuid_ops")),
	index("idx_listings_listing_type").using("btree", table.listingType.asc().nullsLast().op("text_ops")),
	index("idx_listings_price").using("btree", table.price.asc().nullsLast().op("numeric_ops")),
	index("idx_listings_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("idx_listings_workspace_id").using("btree", table.workspaceId.asc().nullsLast().op("uuid_ops")),
	
	// Foreign Keys
	foreignKey({
		columns: [table.workspaceId],
		foreignColumns: [workspaces.id],
		name: "listings_workspace_id_fkey"
	}).onDelete("cascade"),
	
	// Constraints
	check("listings_status_check", sql`status = ANY (ARRAY['draft'::text, 'active'::text, 'pending'::text, 'sold'::text, 'withdrawn'::text])`),
	check("listings_listing_type_check", sql`listing_type = ANY (ARRAY['sale'::text, 'lease'::text, 'business_sale'::text])`),
	check("listings_team_visibility_check", sql`team_visibility = ANY (ARRAY['all'::text, 'assigned'::text, 'custom'::text])`),
	
	// RLS Policies
	pgPolicy("listings_select_policy", { 
		as: "permissive", 
		for: "select", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND (team_visibility = 'all' OR assigned_to = auth.uid() OR created_by = auth.uid() OR user_has_min_role('admin'))` 
	}),
	pgPolicy("listings_insert_policy", { 
		as: "permissive", 
		for: "insert", 
		to: [authenticatedRole], 
		withCheck: sql`workspace_id = get_user_workspace_id() AND user_has_min_role('broker') AND created_by = auth.uid()` 
	}),
	pgPolicy("listings_update_policy", { 
		as: "permissive", 
		for: "update", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR assigned_to = auth.uid() OR user_has_min_role('admin'))` 
	}),
	pgPolicy("listings_delete_policy", { 
		as: "permissive", 
		for: "delete", 
		to: [authenticatedRole], 
		using: sql`workspace_id = get_user_workspace_id() AND (created_by = auth.uid() OR user_has_min_role('admin'))` 
	}),
]);

// ============================================================================
// SCHEMA EXPORTS
// ============================================================================

export const schema = {
	workspaces,
	userProfiles,
	workspaceInvitations,
	listings,
};

// Export types for TypeScript
export type Workspace = typeof workspaces.$inferSelect;
export type NewWorkspace = typeof workspaces.$inferInsert;

export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;

export type WorkspaceInvitation = typeof workspaceInvitations.$inferSelect;
export type NewWorkspaceInvitation = typeof workspaceInvitations.$inferInsert;

export type Listing = typeof listings.$inferSelect;
export type NewListing = typeof listings.$inferInsert;

// ============================================================================
// HELPER TYPES FOR BUSINESS LOGIC
// ============================================================================

export type UserRole = 'owner' | 'admin' | 'broker' | 'viewer';
export type WorkspaceStatus = 'active' | 'suspended' | 'trial' | 'cancelled';
export type SubscriptionPlan = 'trial' | 'basic' | 'pro' | 'enterprise';
export type CompanyType = 'individual' | 'team' | 'firm';
export type ListingStatus = 'draft' | 'active' | 'pending' | 'sold' | 'withdrawn';
export type ListingType = 'sale' | 'lease' | 'business_sale';
export type TeamVisibility = 'all' | 'assigned' | 'custom';
export type InvitationRole = 'admin' | 'broker' | 'viewer'; 