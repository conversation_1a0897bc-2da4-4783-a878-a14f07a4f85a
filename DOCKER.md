# Docker Setup

This project includes Docker Compose configuration for running the backend services with <PERSON><PERSON> in a containerized environment.

## Services

### Redis
- **Image**: redis:7-alpine
- **Port**: 6379
- **Features**: Persistent storage with AOF, health checks
- **Data**: Stored in named volume `injurylaw-redis-data`

### Backend
- **Build**: Custom Dockerfile using Bun runtime
- **Port**: 3001
- **Features**: Hot reloading, shared types integration
- **Dependencies**: Redis service with health check

## Quick Start

1. **Copy environment file**:
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

2. **Start services**:
   ```bash
   # Production mode (detached)
   bun run docker:up
   
   # Development mode (with logs)
   bun run docker:dev
   ```

3. **View logs**:
   ```bash
   bun run docker:logs
   ```

4. **Stop services**:
   ```bash
   bun run docker:down
   ```

5. **Clean up (removes volumes)**:
   ```bash
   bun run docker:clean
   ```

## Development Features

- **Hot Reloading**: Source code changes are automatically reflected
- **Volume Mounting**: Development files are mounted for live updates
- **Health Checks**: Redis health check ensures proper startup order
- **Networking**: Services communicate via dedicated Docker network

## Environment Variables

Required environment variables (set in `.env` file):
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key

Optional overrides:
- `REDIS_PORT`: Redis port (default: 6379)
- `BACKEND_PORT`: Backend port (default: 3001)

## Troubleshooting

- **Redis connection issues**: Check if Redis service is healthy with `docker compose ps`
- **Backend build failures**: Ensure all dependencies are properly installed
- **Port conflicts**: Modify port mappings in docker-compose.yml if needed