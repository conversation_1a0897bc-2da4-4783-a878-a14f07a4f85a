# Rendyr Broker Backend

A modern, high-performance backend API built with [Bun](https://bun.sh/), [<PERSON><PERSON>](https://hono.dev/), and [Drizzle ORM](https://orm.drizzle.team/). This backend provides RESTful APIs with integrated authentication, caching, and database management for the Rendyr Broker application.

## 🚀 Features

- **⚡ Ultra-fast Runtime**: Built with Bun for superior performance
- **🔥 Modern Web Framework**: Hono for lightweight, fast HTTP handling
- **📊 Type-safe Database**: Drizzle ORM with PostgreSQL and Supabase integration
- **🗄️ Redis Caching**: Built-in Redis support for high-performance caching
- **🔐 Authentication Ready**: Supabase Auth integration with user profiles
- **🐳 Docker Support**: Complete containerization with Docker Compose
- **🛡️ Production Ready**: Comprehensive error handling, logging, and health checks
- **📝 Auto-migrations**: Database schema versioning with Drizzle Kit
- **🧪 Testing Suite**: Built-in API testing and health monitoring

## 📋 Prerequisites

- **Bun** >= 1.0.0
- **Node.js** >= 18.0.0
- **PostgreSQL** (or Supabase project)
- **Redis** (optional, for caching)
- **Docker & Docker Compose** (for containerized development)

## 🛠️ Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd rendyr-broker-app
bun install
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```env
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/database
# or
POSTGRES_URL=postgresql://user:password@localhost:5432/database

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_KEY=your_service_key

# Server Configuration
PORT=3001
NODE_ENV=development

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# CORS Configuration (Development)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 3. Database Setup

```bash
# Run database migrations
bun run migrate

# Open Drizzle Studio (optional)
bun run studio
```

### 4. Start Development Server

```bash
# Start with hot reloading
bun run dev

# Or start production build
bun run build
bun run start
```

The server will be running at `http://localhost:3001`

## 🐳 Docker Development

For a complete development environment with Redis:

```bash
# Start all services
bun run docker:up

# View logs
bun run docker:logs

# Stop services
bun run docker:down

# Clean up (removes volumes)
bun run docker:clean
```

## 📚 API Documentation

### Base URL
```
http://localhost:3001
```

### Endpoints

#### Root & Health
- `GET /` - Server information and available endpoints
- `GET /health` - Basic health check
- `GET /api/health-checks` - Database health monitoring

#### API Testing
- `GET /api/test` - Basic API connectivity test
- `GET /api/test-db` - Database connection and schema verification
- `GET /api/test-supabase` - Supabase integration test
- `GET /api/test-integration` - Comprehensive integration testing

#### Users
- `GET /api/users` - User management endpoints
- More user endpoints available (see `src/routes/users.ts`)

### Example Response Format

```json
{
  "success": true,
  "data": {
    "message": "Backend API Server",
    "version": "1.0.0",
    "environment": "development",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "error": "Error type",
  "message": "Detailed error description"
}
```

## 🗄️ Database Schema

The application uses a multi-tenant architecture with the following main tables:

### Core Tables

#### `health_checks`
- Connection and system health monitoring
- Fields: `id`, `status`, `message`, `created_at`

#### `user_profiles`
- Extended user information beyond Supabase auth
- Fields: `id`, `user_id` (FK to auth.users), `display_name`, `bio`, `is_active`, `created_at`, `updated_at`

### Authentication
- Integrates with Supabase Auth (`auth.users` table)
- Custom user profiles extend the base auth functionality

## 🏗️ Architecture

### Technology Stack
- **Runtime**: Bun
- **Framework**: Hono
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Supabase Auth
- **Caching**: Redis with ioredis
- **Containerization**: Docker & Docker Compose

### Project Structure

```
src/
├── config.ts              # Environment configuration
├── database.ts            # Database connection setup
├── index.ts               # Application entry point
├── migrate.ts             # Migration runner
├── lib/
│   ├── cache.ts           # Redis caching service
│   └── supabase.ts        # Supabase client configuration
├── middleware/
│   └── index.ts           # CORS, logging, error handling
├── migrations/
│   ├── 001_initial.sql    # Database schema setup
│   └── 002_multi_tenant_schema.sql
├── routes/
│   ├── index.ts           # Route aggregation
│   ├── api.ts             # API testing endpoints
│   ├── health.ts          # Health check endpoints
│   ├── health-checks.ts   # Database health monitoring
│   └── users.ts           # User management
└── schema/
    └── index.ts           # Drizzle schema definitions
```

### Middleware Stack
- **Error Handling**: Comprehensive error catching and formatting
- **CORS**: Configurable cross-origin resource sharing
- **Logging**: Request/response logging with Hono logger
- **JSON Validation**: Request body validation
- **Pretty JSON**: Formatted JSON responses in development

## 🧪 Testing

### Health Checks
```bash
# Basic server health
curl http://localhost:3001/health

# Database connectivity
curl http://localhost:3001/api/test-db

# Supabase integration
curl http://localhost:3001/api/test-supabase

# Full integration test
curl http://localhost:3001/api/test-integration
```

### Manual Testing
The application includes comprehensive testing endpoints:
- Database connection verification
- Schema validation
- CRUD operations testing
- Supabase Auth integration testing
- Cross-service data consistency checks

## 🚀 Deployment

### Environment Variables (Production)

```env
# Required
DATABASE_URL=postgresql://user:password@host:port/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_key
NODE_ENV=production

# Optional
PORT=3001
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### Docker Production Build

```bash
# Build production image
docker build -t rendyr-broker-backend .

# Run container
docker run -p 3001:3001 --env-file .env rendyr-broker-backend
```

### Using Docker Compose

The included `docker-compose.yml` provides:
- Automatic Redis setup
- Development hot reloading
- Volume management
- Health checks
- Network isolation

## 🔧 Development

### Available Scripts

```bash
# Application
bun run dev          # Start development server with hot reload
bun run build        # Build for production
bun run start        # Start production server
bun run test         # Run test suite

# Database & Migrations
bun run migrate      # Run database migrations (custom script)
bun run db:generate  # Generate migration files from schema changes
bun run db:migrate   # Apply migrations to database
bun run db:push      # Push schema changes directly to database (dev only)
bun run db:pull      # Pull schema from database to local files
bun run db:check     # Check for schema drift and conflicts
bun run db:up        # Upgrade Drizzle Kit to latest version
bun run db:studio    # Open Drizzle Studio
bun run studio       # Alias for db:studio

# Docker commands
bun run docker:up    # Start Docker services
bun run docker:down  # Stop Docker services
bun run docker:logs  # View service logs
bun run docker:clean # Clean up volumes
```

### Database Migrations

```bash
# Generate migration files from schema changes
bun run db:generate

# Apply migrations to database using Drizzle Kit
bun run db:migrate

# Alternative: Apply migrations using custom script
bun run migrate

# Push schema changes directly (development only - bypasses migrations)
bun run db:push

# Pull current database schema to local files
bun run db:pull

# Check for schema drift between local and database
bun run db:check

# Open Drizzle Studio for database management
bun run db:studio
# or
bun run studio
```

#### Development Workflow

**Option 1: Migration-based (Recommended for production)**
```bash
# 1. Modify schema in src/schema/index.ts
# 2. Generate migration files
bun run db:generate
# 3. Review generated migration files in src/migrations/
# 4. Apply migrations
bun run db:migrate
```

**Option 2: Direct push (Development only)**
```bash
# 1. Modify schema in src/schema/index.ts
# 2. Push changes directly to database
bun run db:push
```

### Adding New Routes

1. Create route file in `src/routes/`
2. Export Hono app instance
3. Add to `src/routes/index.ts`
4. Routes automatically mounted with middleware

### Cache Service Usage

```typescript
import { cacheService } from './lib/cache'

// Set cache with TTL
await cacheService.set('key', 'value', 3600) // 1 hour

// Get from cache
const value = await cacheService.get('key')

// Delete from cache
await cacheService.del('key')

// Check existence
const exists = await cacheService.exists('key')
```

## 🔍 Monitoring

### Health Monitoring
- `GET /health` - Basic server status
- `GET /api/health-checks` - Database connectivity
- `GET /api/test-integration` - Full system test

### Logging
- Request/response logging via Hono middleware
- Error logging with stack traces
- Redis connection status
- Database query logging (in development)

## 🛡️ Security

- **Environment Variables**: Sensitive data stored in environment variables
- **CORS**: Configurable cross-origin policies
- **Input Validation**: JSON request validation
- **Error Handling**: Secure error responses without sensitive data exposure
- **Supabase Integration**: Leverages Supabase's built-in security features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary.

## 🆘 Support

For support, please contact the development team or create an issue in the repository.

---

Built with ❤️ using Bun, Hono, and modern web technologies. 