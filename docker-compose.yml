services:
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend
    ports:
      - "3001:3001"
    env_file:
      - .env
    environment:
      - NODE_ENV=development
      - PORT=3001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
    volumes:
      # Mount source code for hot reloading in development
      - ./src:/app/src:ro
      - ./package.json:/app/package.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
    networks:
      - app-network
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: rebuild
          path: ./package.json

networks:
  app-network:
    driver: bridge
    name: network

volumes:
  redis_data:
    name: redis-data
