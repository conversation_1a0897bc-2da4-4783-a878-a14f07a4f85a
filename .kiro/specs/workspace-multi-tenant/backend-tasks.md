# Backend Implementation Tasks

## Database Schema and Infrastructure

- [x] 1. Create database schema and migrations
  - Create Supabase migration files for workspaces, user_profiles, and workspace_invitations tables
  - Add workspace_id column to existing listings table
  - Implement foreign key constraints and indexes
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5_

- [-] 2. Implement Row Level Security policies
  - Configure Drizzle migrations for RLS role management
    - Enable role management in `drizzle.config.ts` with `entities: { roles: { provider: 'supabase' } }`
    - Import predefined Supabase roles from `drizzle-orm/supabase`
  - Define workspace-scoped RLS policies using Drizzle
    - Create policies for `user_profiles` table with workspace filtering using `pgPolicy`
    - Create policies for `workspaces` table with owner/member access control
    - Create policies for `listings` table with workspace-based isolation
    - Create policies for `workspace_invitations` table with workspace and role filtering
  - Implement RLS policy definitions in schema
    - Use `pgPolicy` with `authenticatedRole` from Supabase for authenticated users
    - Define `using` clauses for row filtering based on workspace membership
    - Define `withCheck` clauses for insert/update validation
    - Apply policies for all CRUD operations (select, insert, update, delete)
  - Create workspace context management functions
    - Implement helper functions for workspace access validation
    - Use Supabase `auth.uid()` function for user identification
    - Create workspace membership verification logic
  - Test cross-workspace data access prevention
    - Verify RLS policies prevent access to other workspace data
    - Test with different user roles and workspace memberships
    - Validate policy effectiveness for all table operations
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 3. Create database functions and triggers
  - Implement workspace creation function with owner assignment
  - Create user profile creation trigger on auth.users insert
  - Add updated_at timestamp triggers for all tables
  - _Requirements: 1.1, 1.2, 2.1_

## API and Business Logic

- [ ] 4. Implement authentication backend services
  - Create workspace-aware JWT token handling
  - Implement session management with workspace context
  - Add email verification and password recovery endpoints
  - Handle multi-workspace user authentication
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 5. Create workspace management API
  - Implement workspace CRUD operations
  - Add workspace member management endpoints
  - Create workspace settings and configuration API
  - Implement workspace status and subscription management
  - _Requirements: 1.3, 1.4, 1.5, 8.1, 8.2_

- [ ] 6. Implement team invitation backend
  - Create invitation token generation and validation
  - Implement email sending service for invitations
  - Add invitation status tracking and management
  - Handle invitation acceptance and user linking
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

## Data Management and Migration

- [ ] 7. Create data migration scripts
  - Implement existing listings migration to workspace model
  - Add workspace_id assignment to current data
  - Create data integrity validation scripts
  - Implement rollback procedures for failed migrations
  - _Requirements: 6.5, 10.2_

- [ ] 8. Implement workspace-scoped data queries
  - Update all data access patterns to include workspace filtering
  - Create optimized queries for multi-tenant data access
  - Implement data aggregation and reporting by workspace
  - Add performance monitoring for workspace queries
  - _Requirements: 6.2, 9.1, 9.2_

## Security and Access Control

- [ ] 9. Implement role-based access control backend
  - Create permission validation functions
  - Implement role hierarchy enforcement
  - Add API endpoint protection based on roles
  - Create audit logging for permission changes
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6_

- [ ] 10. Add security monitoring and logging
  - Implement authentication attempt logging
  - Add workspace access monitoring
  - Create security event alerting
  - Implement rate limiting and abuse prevention
  - _Requirements: 2.4, 6.1, 8.5_

## Real-time Features and Notifications

- [ ] 11. Implement real-time subscriptions
  - Create Supabase real-time subscriptions for workspace data
  - Add team member activity notifications
  - Implement listing assignment and collaboration updates
  - Create workspace-scoped notification channels
  - _Requirements: 9.3, 9.4, 9.5_

- [ ] 12. Create notification and email services
  - Implement branded email templates for workspaces
  - Add notification delivery system
  - Create email queue and retry mechanisms
  - Implement notification preferences management
  - _Requirements: 3.2, 7.1, 7.2_

## Performance and Scalability

- [ ] 13. Optimize database performance
  - Create appropriate indexes for multi-tenant queries
  - Implement query optimization for workspace-scoped data
  - Add database connection pooling and caching
  - Create performance monitoring and alerting
  - _Requirements: 6.2, 10.4, 10.5_

- [ ] 14. Implement caching and optimization
  - Add Redis caching for frequently accessed workspace data
  - Implement API response caching strategies
  - Create background job processing for heavy operations
  - Add database query optimization and monitoring
  - _Requirements: 8.1, 8.2, 9.1_